============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Fri Jul 11 08:33:21 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 23 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.147640s wall, 1.375000s user + 3.750000s system = 5.125000s CPU (99.6%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.686646s wall, 1.593750s user + 0.093750s system = 1.687500s CPU (100.1%)

RUN-1004 : used memory is 297 MB, reserved memory is 266 MB, peak memory is 300 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1010010110001101
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=78) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=78)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=24,BUS_CTRL_NUM=56,BUS_WIDTH='{32'sb01000,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000},BUS_CTRL_POS='{32'sb0,32'sb010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 21740/12 useful/useless nets, 18788/4 useful/useless insts
SYN-1016 : Merged 17 instances.
SYN-1032 : 21516/16 useful/useless nets, 19112/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 279 better
SYN-1014 : Optimize round 2
SYN-1032 : 21307/30 useful/useless nets, 18903/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.258738s wall, 2.187500s user + 0.062500s system = 2.250000s CPU (99.6%)

RUN-1004 : used memory is 321 MB, reserved memory is 288 MB, peak memory is 323 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 21331/155 useful/useless nets, 18948/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 21702/5 useful/useless nets, 19319/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78746, tnet num: 21702, tinst num: 19318, tnode num: 110776, tedge num: 122954.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.162639s wall, 1.140625s user + 0.015625s system = 1.156250s CPU (99.5%)

RUN-1004 : used memory is 456 MB, reserved memory is 424 MB, peak memory is 456 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21702 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.57), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 387 instances into 173 LUTs, name keeping = 77%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 290 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 108 adder to BLE ...
SYN-4008 : Packed 108 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.338073s wall, 4.250000s user + 0.093750s system = 4.343750s CPU (100.1%)

RUN-1004 : used memory is 356 MB, reserved memory is 335 MB, peak memory is 560 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  6.939064s wall, 6.734375s user + 0.171875s system = 6.906250s CPU (99.5%)

RUN-1004 : used memory is 357 MB, reserved memory is 336 MB, peak memory is 560 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (177 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 18745 instances
RUN-0007 : 5316 luts, 11929 seqs, 923 mslices, 489 lslices, 60 pads, 23 brams, 0 dsps
RUN-1001 : There are total 21152 nets
RUN-1001 : 15995 nets have 2 pins
RUN-1001 : 4040 nets have [3 - 5] pins
RUN-1001 : 779 nets have [6 - 10] pins
RUN-1001 : 216 nets have [11 - 20] pins
RUN-1001 : 104 nets have [21 - 99] pins
RUN-1001 : 18 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4756     
RUN-1001 :   No   |  No   |  Yes  |     606     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     341     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  112  |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 120
PHY-3001 : Initial placement ...
PHY-3001 : design contains 18743 instances, 5316 luts, 11929 seqs, 1412 slices, 279 macros(1412 instances: 923 mslices 489 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 61%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77545, tnet num: 21150, tinst num: 18743, tnode num: 109405, tedge num: 121818.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.157307s wall, 1.140625s user + 0.015625s system = 1.156250s CPU (99.9%)

RUN-1004 : used memory is 514 MB, reserved memory is 486 MB, peak memory is 560 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21150 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.053272s wall, 2.000000s user + 0.046875s system = 2.046875s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.42758e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 18743.
PHY-3001 : Level 1 #clusters 2058.
PHY-3001 : End clustering;  0.154606s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (151.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 61%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 834908, overlap = 595.188
PHY-3002 : Step(2): len = 757027, overlap = 646.188
PHY-3002 : Step(3): len = 485436, overlap = 798.094
PHY-3002 : Step(4): len = 424964, overlap = 883
PHY-3002 : Step(5): len = 328634, overlap = 1008.34
PHY-3002 : Step(6): len = 292814, overlap = 1066.38
PHY-3002 : Step(7): len = 243503, overlap = 1129.91
PHY-3002 : Step(8): len = 218615, overlap = 1182.97
PHY-3002 : Step(9): len = 194914, overlap = 1222.12
PHY-3002 : Step(10): len = 178369, overlap = 1260.47
PHY-3002 : Step(11): len = 159222, overlap = 1300.66
PHY-3002 : Step(12): len = 150188, overlap = 1335.56
PHY-3002 : Step(13): len = 136802, overlap = 1363.97
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.07834e-06
PHY-3002 : Step(14): len = 140319, overlap = 1350.88
PHY-3002 : Step(15): len = 166799, overlap = 1220.03
PHY-3002 : Step(16): len = 181376, overlap = 1153.97
PHY-3002 : Step(17): len = 185947, overlap = 1075.53
PHY-3002 : Step(18): len = 182019, overlap = 1056.19
PHY-3002 : Step(19): len = 178220, overlap = 1024.81
PHY-3002 : Step(20): len = 174191, overlap = 997.062
PHY-3002 : Step(21): len = 170023, overlap = 996.656
PHY-3002 : Step(22): len = 167159, overlap = 1003.97
PHY-3002 : Step(23): len = 163457, overlap = 1007.59
PHY-3002 : Step(24): len = 160809, overlap = 1020.62
PHY-3002 : Step(25): len = 159563, overlap = 1022.38
PHY-3002 : Step(26): len = 158238, overlap = 1038.69
PHY-3002 : Step(27): len = 155796, overlap = 1052.78
PHY-3002 : Step(28): len = 154567, overlap = 1056.47
PHY-3002 : Step(29): len = 153981, overlap = 1063.59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.15668e-06
PHY-3002 : Step(30): len = 161196, overlap = 1050.81
PHY-3002 : Step(31): len = 176021, overlap = 973.25
PHY-3002 : Step(32): len = 180226, overlap = 931.438
PHY-3002 : Step(33): len = 181770, overlap = 903.562
PHY-3002 : Step(34): len = 182421, overlap = 913.812
PHY-3002 : Step(35): len = 181330, overlap = 910.812
PHY-3002 : Step(36): len = 180330, overlap = 888.281
PHY-3002 : Step(37): len = 179300, overlap = 893.344
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.31335e-06
PHY-3002 : Step(38): len = 190395, overlap = 830.406
PHY-3002 : Step(39): len = 205111, overlap = 742.156
PHY-3002 : Step(40): len = 209878, overlap = 713.656
PHY-3002 : Step(41): len = 211097, overlap = 702.75
PHY-3002 : Step(42): len = 210771, overlap = 696.719
PHY-3002 : Step(43): len = 208778, overlap = 691.781
PHY-3002 : Step(44): len = 207661, overlap = 712.125
PHY-3002 : Step(45): len = 206958, overlap = 712.594
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.6267e-06
PHY-3002 : Step(46): len = 218723, overlap = 655.875
PHY-3002 : Step(47): len = 232424, overlap = 605.375
PHY-3002 : Step(48): len = 236766, overlap = 563.781
PHY-3002 : Step(49): len = 240046, overlap = 532.375
PHY-3002 : Step(50): len = 239524, overlap = 531.781
PHY-3002 : Step(51): len = 238180, overlap = 513.312
PHY-3002 : Step(52): len = 235920, overlap = 504.156
PHY-3002 : Step(53): len = 235501, overlap = 503.969
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.72534e-05
PHY-3002 : Step(54): len = 244955, overlap = 487.062
PHY-3002 : Step(55): len = 259711, overlap = 444.062
PHY-3002 : Step(56): len = 263446, overlap = 426.062
PHY-3002 : Step(57): len = 264952, overlap = 403.344
PHY-3002 : Step(58): len = 264240, overlap = 404
PHY-3002 : Step(59): len = 262653, overlap = 393.406
PHY-3002 : Step(60): len = 260545, overlap = 397.562
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.45068e-05
PHY-3002 : Step(61): len = 268425, overlap = 387.719
PHY-3002 : Step(62): len = 280264, overlap = 351.469
PHY-3002 : Step(63): len = 284476, overlap = 324.938
PHY-3002 : Step(64): len = 284849, overlap = 311.938
PHY-3002 : Step(65): len = 283254, overlap = 323.938
PHY-3002 : Step(66): len = 280873, overlap = 319.531
PHY-3002 : Step(67): len = 278757, overlap = 322.812
PHY-3002 : Step(68): len = 277426, overlap = 303.719
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.90136e-05
PHY-3002 : Step(69): len = 284473, overlap = 289.469
PHY-3002 : Step(70): len = 292044, overlap = 265.406
PHY-3002 : Step(71): len = 294822, overlap = 261.219
PHY-3002 : Step(72): len = 295338, overlap = 257.062
PHY-3002 : Step(73): len = 292886, overlap = 255.406
PHY-3002 : Step(74): len = 291209, overlap = 262.375
PHY-3002 : Step(75): len = 289033, overlap = 264.781
PHY-3002 : Step(76): len = 289600, overlap = 259.219
PHY-3002 : Step(77): len = 289058, overlap = 258.062
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000138027
PHY-3002 : Step(78): len = 294223, overlap = 247.562
PHY-3002 : Step(79): len = 301025, overlap = 235.219
PHY-3002 : Step(80): len = 303935, overlap = 225.812
PHY-3002 : Step(81): len = 304259, overlap = 226.062
PHY-3002 : Step(82): len = 303983, overlap = 225.5
PHY-3002 : Step(83): len = 303793, overlap = 205.844
PHY-3002 : Step(84): len = 304025, overlap = 203.125
PHY-3002 : Step(85): len = 305899, overlap = 207.594
PHY-3002 : Step(86): len = 305734, overlap = 207.938
PHY-3002 : Step(87): len = 306102, overlap = 211.844
PHY-3002 : Step(88): len = 306129, overlap = 200.75
PHY-3002 : Step(89): len = 305984, overlap = 215.312
PHY-3002 : Step(90): len = 305801, overlap = 203.531
PHY-3002 : Step(91): len = 305819, overlap = 217.656
PHY-3002 : Step(92): len = 305859, overlap = 223.656
PHY-3002 : Step(93): len = 305742, overlap = 225.719
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000276054
PHY-3002 : Step(94): len = 308336, overlap = 224.469
PHY-3002 : Step(95): len = 312124, overlap = 223.031
PHY-3002 : Step(96): len = 313445, overlap = 221.938
PHY-3002 : Step(97): len = 314533, overlap = 212.969
PHY-3002 : Step(98): len = 314213, overlap = 213.062
PHY-3002 : Step(99): len = 314132, overlap = 208.469
PHY-3002 : Step(100): len = 312822, overlap = 198.156
PHY-3002 : Step(101): len = 313335, overlap = 192.125
PHY-3002 : Step(102): len = 313570, overlap = 199.5
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000523998
PHY-3002 : Step(103): len = 315225, overlap = 201.062
PHY-3002 : Step(104): len = 318419, overlap = 187
PHY-3002 : Step(105): len = 319659, overlap = 178.062
PHY-3002 : Step(106): len = 321128, overlap = 173.75
PHY-3002 : Step(107): len = 321618, overlap = 162.875
PHY-3002 : Step(108): len = 321046, overlap = 163.5
PHY-3002 : Step(109): len = 320797, overlap = 152.406
PHY-3002 : Step(110): len = 320314, overlap = 151.188
PHY-3002 : Step(111): len = 320310, overlap = 161.719
PHY-3002 : Step(112): len = 320055, overlap = 166.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011279s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21152.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 416504, over cnt = 1131(3%), over = 5079, worst = 44
PHY-1001 : End global iterations;  0.832199s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (146.4%)

PHY-1001 : Congestion index: top1 = 72.91, top5 = 51.74, top10 = 42.04, top15 = 36.52.
PHY-3001 : End congestion estimation;  1.050741s wall, 1.375000s user + 0.046875s system = 1.421875s CPU (135.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21150 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.943248s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.71435e-05
PHY-3002 : Step(113): len = 354570, overlap = 157
PHY-3002 : Step(114): len = 367041, overlap = 149.094
PHY-3002 : Step(115): len = 366065, overlap = 140.562
PHY-3002 : Step(116): len = 365608, overlap = 132.812
PHY-3002 : Step(117): len = 369283, overlap = 125.531
PHY-3002 : Step(118): len = 374683, overlap = 128.469
PHY-3002 : Step(119): len = 383528, overlap = 125.438
PHY-3002 : Step(120): len = 386097, overlap = 129.688
PHY-3002 : Step(121): len = 386644, overlap = 136.094
PHY-3002 : Step(122): len = 389487, overlap = 142.406
PHY-3002 : Step(123): len = 390481, overlap = 144.25
PHY-3002 : Step(124): len = 391848, overlap = 149.781
PHY-3002 : Step(125): len = 394058, overlap = 155.156
PHY-3002 : Step(126): len = 393544, overlap = 151.312
PHY-3002 : Step(127): len = 393851, overlap = 149.562
PHY-3002 : Step(128): len = 395126, overlap = 149.469
PHY-3002 : Step(129): len = 396474, overlap = 147.688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000174287
PHY-3002 : Step(130): len = 396400, overlap = 149.25
PHY-3002 : Step(131): len = 398709, overlap = 146.281
PHY-3002 : Step(132): len = 401891, overlap = 147.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000348574
PHY-3002 : Step(133): len = 407644, overlap = 145.219
PHY-3002 : Step(134): len = 417740, overlap = 139.531
PHY-3002 : Step(135): len = 422764, overlap = 139.75
PHY-3002 : Step(136): len = 424685, overlap = 141.312
PHY-3002 : Step(137): len = 427400, overlap = 140.375
PHY-3002 : Step(138): len = 427436, overlap = 137
PHY-3002 : Step(139): len = 427184, overlap = 132.781
PHY-3002 : Step(140): len = 427932, overlap = 134.5
PHY-3002 : Step(141): len = 427845, overlap = 135.031
PHY-3002 : Step(142): len = 426579, overlap = 134.594
PHY-3002 : Step(143): len = 427368, overlap = 133.125
PHY-3002 : Step(144): len = 427408, overlap = 132.969
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000697148
PHY-3002 : Step(145): len = 428736, overlap = 131.906
PHY-3002 : Step(146): len = 431956, overlap = 127.719
PHY-3002 : Step(147): len = 437784, overlap = 128.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00132079
PHY-3002 : Step(148): len = 438894, overlap = 129.156
PHY-3002 : Step(149): len = 442167, overlap = 123.031
PHY-3002 : Step(150): len = 453965, overlap = 124.688
PHY-3002 : Step(151): len = 457410, overlap = 122.906
PHY-3002 : Step(152): len = 458117, overlap = 124.594
PHY-3002 : Step(153): len = 457235, overlap = 118.406
PHY-3002 : Step(154): len = 456583, overlap = 115.875
PHY-3002 : Step(155): len = 456183, overlap = 111.562
PHY-3002 : Step(156): len = 456181, overlap = 111.781
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 67%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/21152.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 519312, over cnt = 2091(5%), over = 10050, worst = 47
PHY-1001 : End global iterations;  1.105508s wall, 1.687500s user + 0.046875s system = 1.734375s CPU (156.9%)

PHY-1001 : Congestion index: top1 = 86.88, top5 = 62.56, top10 = 52.34, top15 = 46.62.
PHY-3001 : End congestion estimation;  1.413048s wall, 2.000000s user + 0.046875s system = 2.046875s CPU (144.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21150 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.154611s wall, 1.140625s user + 0.031250s system = 1.171875s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000117644
PHY-3002 : Step(157): len = 459395, overlap = 360.906
PHY-3002 : Step(158): len = 457394, overlap = 316.938
PHY-3002 : Step(159): len = 451106, overlap = 285.875
PHY-3002 : Step(160): len = 443818, overlap = 250.25
PHY-3002 : Step(161): len = 437503, overlap = 246.656
PHY-3002 : Step(162): len = 433512, overlap = 229.531
PHY-3002 : Step(163): len = 429479, overlap = 220.5
PHY-3002 : Step(164): len = 427958, overlap = 215
PHY-3002 : Step(165): len = 426771, overlap = 220.375
PHY-3002 : Step(166): len = 424205, overlap = 228.312
PHY-3002 : Step(167): len = 422433, overlap = 218.5
PHY-3002 : Step(168): len = 422020, overlap = 216.281
PHY-3002 : Step(169): len = 419629, overlap = 216.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000235288
PHY-3002 : Step(170): len = 419796, overlap = 200.969
PHY-3002 : Step(171): len = 421188, overlap = 193.844
PHY-3002 : Step(172): len = 422704, overlap = 184.781
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000470576
PHY-3002 : Step(173): len = 425530, overlap = 171.844
PHY-3002 : Step(174): len = 430550, overlap = 155.875
PHY-3002 : Step(175): len = 433023, overlap = 148.781
PHY-3002 : Step(176): len = 434756, overlap = 145.938
PHY-3002 : Step(177): len = 435689, overlap = 143.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 77545, tnet num: 21150, tinst num: 18743, tnode num: 109405, tedge num: 121818.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.522767s wall, 1.515625s user + 0.015625s system = 1.531250s CPU (100.6%)

RUN-1004 : used memory is 555 MB, reserved memory is 529 MB, peak memory is 683 MB
OPT-1001 : Total overflow 475.69 peak overflow 4.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 444/21152.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 512968, over cnt = 2308(6%), over = 7814, worst = 26
PHY-1001 : End global iterations;  1.216655s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (172.1%)

PHY-1001 : Congestion index: top1 = 57.76, top5 = 46.53, top10 = 41.38, top15 = 38.21.
PHY-1001 : End incremental global routing;  1.477989s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (159.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21150 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.023606s wall, 1.015625s user + 0.000000s system = 1.015625s CPU (99.2%)

OPT-1001 : 16 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 18664 has valid locations, 225 needs to be replaced
PHY-3001 : design contains 18952 instances, 5410 luts, 12044 seqs, 1412 slices, 279 macros(1412 instances: 923 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 452065
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16569/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 524904, over cnt = 2322(6%), over = 7877, worst = 26
PHY-1001 : End global iterations;  0.184542s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (135.5%)

PHY-1001 : Congestion index: top1 = 58.06, top5 = 46.86, top10 = 41.79, top15 = 38.54.
PHY-3001 : End congestion estimation;  0.557421s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (109.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 78225, tnet num: 21359, tinst num: 18952, tnode num: 110350, tedge num: 122760.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.485593s wall, 1.453125s user + 0.046875s system = 1.500000s CPU (101.0%)

RUN-1004 : used memory is 595 MB, reserved memory is 578 MB, peak memory is 686 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.544487s wall, 2.468750s user + 0.093750s system = 2.562500s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(178): len = 451852, overlap = 2.25
PHY-3002 : Step(179): len = 452770, overlap = 2.25
PHY-3002 : Step(180): len = 453403, overlap = 2.1875
PHY-3002 : Step(181): len = 454779, overlap = 2.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 68%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16592/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 522576, over cnt = 2330(6%), over = 7918, worst = 26
PHY-1001 : End global iterations;  0.186700s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 58.34, top5 = 46.94, top10 = 41.80, top15 = 38.62.
PHY-3001 : End congestion estimation;  0.444680s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (101.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.136830s wall, 1.109375s user + 0.015625s system = 1.125000s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000734672
PHY-3002 : Step(182): len = 454804, overlap = 144.5
PHY-3002 : Step(183): len = 455142, overlap = 144.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00146934
PHY-3002 : Step(184): len = 455157, overlap = 144.938
PHY-3002 : Step(185): len = 455528, overlap = 145.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00293869
PHY-3002 : Step(186): len = 455586, overlap = 144.844
PHY-3002 : Step(187): len = 455848, overlap = 144.844
PHY-3001 : Final: Len = 455848, Over = 144.844
PHY-3001 : End incremental placement;  5.704140s wall, 6.234375s user + 0.296875s system = 6.531250s CPU (114.5%)

OPT-1001 : Total overflow 480.19 peak overflow 4.69
OPT-1001 : End high-fanout net optimization;  8.729288s wall, 10.171875s user + 0.328125s system = 10.500000s CPU (120.3%)

OPT-1001 : Current memory(MB): used = 689, reserve = 668, peak = 704.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16592/21361.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 525304, over cnt = 2279(6%), over = 7418, worst = 26
PHY-1002 : len = 566448, over cnt = 1434(4%), over = 3394, worst = 18
PHY-1002 : len = 595784, over cnt = 596(1%), over = 1264, worst = 15
PHY-1002 : len = 611160, over cnt = 181(0%), over = 356, worst = 11
PHY-1002 : len = 616392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.170304s wall, 1.875000s user + 0.000000s system = 1.875000s CPU (160.2%)

PHY-1001 : Congestion index: top1 = 49.98, top5 = 42.92, top10 = 39.27, top15 = 37.07.
OPT-1001 : End congestion update;  1.422306s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (149.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 21359 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.880399s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (99.4%)

OPT-0007 : Start: WNS 3837 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.308774s wall, 3.000000s user + 0.015625s system = 3.015625s CPU (130.6%)

OPT-1001 : Current memory(MB): used = 666, reserve = 648, peak = 704.
OPT-1001 : End physical optimization;  12.881880s wall, 15.140625s user + 0.375000s system = 15.515625s CPU (120.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5410 LUT to BLE ...
SYN-4008 : Packed 5410 LUT and 2607 SEQ to BLE.
SYN-4003 : Packing 9437 remaining SEQ's ...
SYN-4005 : Packed 3182 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 6255 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11665/13321 primitive instances ...
PHY-3001 : End packing;  2.667252s wall, 2.656250s user + 0.000000s system = 2.656250s CPU (99.6%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 7887 instances
RUN-1001 : 3900 mslices, 3899 lslices, 60 pads, 23 brams, 0 dsps
RUN-1001 : There are total 18808 nets
RUN-1001 : 13312 nets have 2 pins
RUN-1001 : 4168 nets have [3 - 5] pins
RUN-1001 : 851 nets have [6 - 10] pins
RUN-1001 : 332 nets have [11 - 20] pins
RUN-1001 : 136 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 7885 instances, 7799 slices, 279 macros(1412 instances: 923 mslices 489 lslices)
PHY-3001 : Cell area utilization is 83%
PHY-3001 : After packing: Len = 474284, Over = 351.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 7459/18808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591784, over cnt = 1386(3%), over = 2305, worst = 9
PHY-1002 : len = 598816, over cnt = 891(2%), over = 1234, worst = 6
PHY-1002 : len = 610584, over cnt = 296(0%), over = 387, worst = 5
PHY-1002 : len = 614368, over cnt = 143(0%), over = 181, worst = 4
PHY-1002 : len = 617896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.102145s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (164.5%)

PHY-1001 : Congestion index: top1 = 52.05, top5 = 43.46, top10 = 39.63, top15 = 37.26.
PHY-3001 : End congestion estimation;  1.425505s wall, 2.125000s user + 0.015625s system = 2.140625s CPU (150.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64893, tnet num: 18806, tinst num: 7885, tnode num: 88306, tedge num: 106888.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.625385s wall, 1.609375s user + 0.015625s system = 1.625000s CPU (100.0%)

RUN-1004 : used memory is 585 MB, reserved memory is 573 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.549416s wall, 2.515625s user + 0.031250s system = 2.546875s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.0128e-05
PHY-3002 : Step(188): len = 476952, overlap = 337.5
PHY-3002 : Step(189): len = 476147, overlap = 348.25
PHY-3002 : Step(190): len = 476531, overlap = 362
PHY-3002 : Step(191): len = 477907, overlap = 376.5
PHY-3002 : Step(192): len = 478963, overlap = 384
PHY-3002 : Step(193): len = 478269, overlap = 380
PHY-3002 : Step(194): len = 477624, overlap = 372.25
PHY-3002 : Step(195): len = 475244, overlap = 361
PHY-3002 : Step(196): len = 473795, overlap = 364.5
PHY-3002 : Step(197): len = 471593, overlap = 363.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100256
PHY-3002 : Step(198): len = 475927, overlap = 350.5
PHY-3002 : Step(199): len = 479482, overlap = 338.75
PHY-3002 : Step(200): len = 479539, overlap = 337
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(201): len = 488420, overlap = 315
PHY-3002 : Step(202): len = 495637, overlap = 305.25
PHY-3002 : Step(203): len = 493254, overlap = 305.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.788696s wall, 0.968750s user + 0.890625s system = 1.859375s CPU (235.8%)

PHY-3001 : Trial Legalized: Len = 593547
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 491/18808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 670808, over cnt = 2223(6%), over = 3574, worst = 7
PHY-1002 : len = 684696, over cnt = 1347(3%), over = 1857, worst = 6
PHY-1002 : len = 700128, over cnt = 536(1%), over = 707, worst = 5
PHY-1002 : len = 710584, over cnt = 59(0%), over = 70, worst = 3
PHY-1002 : len = 711856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.797290s wall, 3.109375s user + 0.078125s system = 3.187500s CPU (177.4%)

PHY-1001 : Congestion index: top1 = 48.71, top5 = 44.01, top10 = 41.10, top15 = 39.34.
PHY-3001 : End congestion estimation;  2.150267s wall, 3.453125s user + 0.078125s system = 3.531250s CPU (164.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.925068s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000201022
PHY-3002 : Step(204): len = 553590, overlap = 72.75
PHY-3002 : Step(205): len = 536624, overlap = 109
PHY-3002 : Step(206): len = 525818, overlap = 145.5
PHY-3002 : Step(207): len = 520535, overlap = 175.75
PHY-3002 : Step(208): len = 517922, overlap = 197.5
PHY-3002 : Step(209): len = 516298, overlap = 204.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000402044
PHY-3002 : Step(210): len = 521306, overlap = 202.5
PHY-3002 : Step(211): len = 525806, overlap = 198.75
PHY-3002 : Step(212): len = 525014, overlap = 203.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000797141
PHY-3002 : Step(213): len = 528375, overlap = 201.5
PHY-3002 : Step(214): len = 534571, overlap = 195
PHY-3002 : Step(215): len = 537044, overlap = 193.5
PHY-3002 : Step(216): len = 539088, overlap = 196.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.033469s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.4%)

PHY-3001 : Legalized: Len = 576705, Over = 0
PHY-3001 : Spreading special nets. 29 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.083869s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.2%)

PHY-3001 : 43 instances has been re-located, deltaX = 11, deltaY = 30, maxDist = 1.
PHY-3001 : Final: Len = 577205, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64893, tnet num: 18806, tinst num: 7885, tnode num: 88306, tedge num: 106888.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.930454s wall, 1.906250s user + 0.031250s system = 1.937500s CPU (100.4%)

RUN-1004 : used memory is 601 MB, reserved memory is 604 MB, peak memory is 704 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3434/18808.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 662632, over cnt = 2015(5%), over = 3156, worst = 8
PHY-1002 : len = 671824, over cnt = 1344(3%), over = 1914, worst = 5
PHY-1002 : len = 690528, over cnt = 357(1%), over = 485, worst = 5
PHY-1002 : len = 695456, over cnt = 119(0%), over = 163, worst = 4
PHY-1002 : len = 698504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.627327s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (166.1%)

PHY-1001 : Congestion index: top1 = 47.18, top5 = 41.96, top10 = 39.45, top15 = 37.80.
PHY-1001 : End incremental global routing;  1.941046s wall, 3.031250s user + 0.000000s system = 3.031250s CPU (156.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.967214s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (98.5%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 7820 has valid locations, 11 needs to be replaced
PHY-3001 : design contains 7894 instances, 7808 slices, 279 macros(1412 instances: 923 mslices 489 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 579033
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16992/18817.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 700056, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 700048, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 700200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.373728s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (108.7%)

PHY-1001 : Congestion index: top1 = 47.18, top5 = 42.05, top10 = 39.56, top15 = 37.91.
PHY-3001 : End congestion estimation;  0.682447s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (105.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64951, tnet num: 18815, tinst num: 7894, tnode num: 88374, tedge num: 106959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.897602s wall, 1.906250s user + 0.000000s system = 1.906250s CPU (100.5%)

RUN-1004 : used memory is 627 MB, reserved memory is 618 MB, peak memory is 704 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.854186s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(217): len = 578851, overlap = 0
PHY-3002 : Step(218): len = 578668, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 83%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 16988/18817.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 699600, over cnt = 22(0%), over = 23, worst = 2
PHY-1002 : len = 699520, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 699640, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 699688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.521705s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (110.8%)

PHY-1001 : Congestion index: top1 = 47.31, top5 = 42.05, top10 = 39.52, top15 = 37.86.
PHY-3001 : End congestion estimation;  0.826790s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (107.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.917064s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000875152
PHY-3002 : Step(219): len = 578645, overlap = 1.75
PHY-3002 : Step(220): len = 578699, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006591s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 578735, Over = 0
PHY-3001 : End spreading;  0.071989s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.5%)

PHY-3001 : Final: Len = 578735, Over = 0
PHY-3001 : End incremental placement;  5.886736s wall, 6.046875s user + 0.140625s system = 6.187500s CPU (105.1%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  9.286537s wall, 10.531250s user + 0.171875s system = 10.703125s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 698, reserve = 682, peak = 704.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 16988/18817.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 699760, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 699776, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 699848, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 699848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.511254s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 41.92, top10 = 39.44, top15 = 37.82.
OPT-1001 : End congestion update;  0.810255s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (104.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.768254s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.7%)

OPT-0007 : Start: WNS 4540 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.583814s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 698, reserve = 682, peak = 704.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.771038s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17001/18817.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 699848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114325s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (109.3%)

PHY-1001 : Congestion index: top1 = 47.33, top5 = 41.92, top10 = 39.44, top15 = 37.82.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.768561s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4540 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 47.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4540ps with logic level 8 
RUN-1001 :       #2 path slack 4540ps with logic level 8 
RUN-1001 :       #3 path slack 4560ps with logic level 8 
RUN-1001 :       #4 path slack 4592ps with logic level 8 
RUN-1001 :       #5 path slack 4592ps with logic level 8 
RUN-1001 :       #6 path slack 4607ps with logic level 8 
RUN-1001 :       #7 path slack 4607ps with logic level 8 
RUN-1001 :       #8 path slack 4636ps with logic level 8 
OPT-1001 : End physical optimization;  15.020309s wall, 16.265625s user + 0.203125s system = 16.468750s CPU (109.6%)

RUN-1003 : finish command "place" in  66.939025s wall, 130.046875s user + 7.765625s system = 137.812500s CPU (205.9%)

RUN-1004 : used memory is 583 MB, reserved memory is 570 MB, peak memory is 704 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.555063s wall, 2.671875s user + 0.000000s system = 2.671875s CPU (171.8%)

RUN-1004 : used memory is 583 MB, reserved memory is 572 MB, peak memory is 704 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 7896 instances
RUN-1001 : 3909 mslices, 3899 lslices, 60 pads, 23 brams, 0 dsps
RUN-1001 : There are total 18817 nets
RUN-1001 : 13314 nets have 2 pins
RUN-1001 : 4167 nets have [3 - 5] pins
RUN-1001 : 854 nets have [6 - 10] pins
RUN-1001 : 336 nets have [11 - 20] pins
RUN-1001 : 137 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64951, tnet num: 18815, tinst num: 7894, tnode num: 88374, tedge num: 106959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.619524s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (99.4%)

RUN-1004 : used memory is 573 MB, reserved memory is 553 MB, peak memory is 704 MB
PHY-1001 : 3909 mslices, 3899 lslices, 60 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 646512, over cnt = 2125(6%), over = 3491, worst = 8
PHY-1002 : len = 660736, over cnt = 1335(3%), over = 1904, worst = 6
PHY-1002 : len = 675520, over cnt = 574(1%), over = 801, worst = 5
PHY-1002 : len = 688000, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 688096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.671464s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 47.39, top5 = 41.99, top10 = 39.37, top15 = 37.66.
PHY-1001 : End global routing;  2.006717s wall, 3.156250s user + 0.031250s system = 3.187500s CPU (158.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 681, reserve = 672, peak = 704.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 951, reserve = 938, peak = 951.
PHY-1001 : End build detailed router design. 4.438278s wall, 4.437500s user + 0.000000s system = 4.437500s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 189552, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.884854s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 986, reserve = 975, peak = 986.
PHY-1001 : End phase 1; 0.892479s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 77% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.61058e+06, over cnt = 1193(0%), over = 1196, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1000, reserve = 987, peak = 1000.
PHY-1001 : End initial routed; 13.097264s wall, 40.828125s user + 0.312500s system = 41.140625s CPU (314.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17622(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.606   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.311285s wall, 3.312500s user + 0.000000s system = 3.312500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1018, reserve = 1007, peak = 1018.
PHY-1001 : End phase 2; 16.408731s wall, 44.140625s user + 0.312500s system = 44.453125s CPU (270.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.61058e+06, over cnt = 1193(0%), over = 1196, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.239354s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (104.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.59982e+06, over cnt = 475(0%), over = 475, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.763534s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (149.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.59968e+06, over cnt = 82(0%), over = 82, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.447201s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (143.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.60047e+06, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.233421s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (120.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.60068e+06, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.211214s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (88.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 1.60092e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 5; 0.184178s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (84.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/17622(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.606   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.262078s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 271 feed throughs used by 233 nets
PHY-1001 : End commit to database; 2.037049s wall, 2.015625s user + 0.000000s system = 2.015625s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 1102, reserve = 1093, peak = 1102.
PHY-1001 : End phase 3; 7.855670s wall, 8.421875s user + 0.000000s system = 8.421875s CPU (107.2%)

PHY-1003 : Routed, final wirelength = 1.60092e+06
PHY-1001 : Current memory(MB): used = 1106, reserve = 1097, peak = 1106.
PHY-1001 : End export database. 0.059132s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.7%)

PHY-1001 : End detail routing;  30.051906s wall, 58.328125s user + 0.343750s system = 58.671875s CPU (195.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64951, tnet num: 18815, tinst num: 7894, tnode num: 88374, tedge num: 106959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.622438s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (100.2%)

RUN-1004 : used memory is 983 MB, reserved memory is 978 MB, peak memory is 1106 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  37.925762s wall, 67.281250s user + 0.437500s system = 67.718750s CPU (178.6%)

RUN-1004 : used memory is 1007 MB, reserved memory is 1005 MB, peak memory is 1106 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8387   out of  19600   42.79%
#reg                    12143   out of  19600   61.95%
#le                     14590
  #lut only              2447   out of  14590   16.77%
  #reg only              6203   out of  14590   42.52%
  #lut&reg               5940   out of  14590   40.71%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  22
  #fifo9k                   1
#bram32k                    0   out of     16    0.00%
#pad                       60   out of    188   31.91%
  #ireg                     9
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6697
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          106
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         B3        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NWE         INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         N1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         E2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         E1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         P5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         F5        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         N5        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F16        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDY        INPUT        K15        LVCMOS33          N/A           N/A            IREG       
    Fog_RXDZ        INPUT         M3        LVCMOS33          N/A           N/A            IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          NONE       
   Version[3]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         N4        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         M5        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         B6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         B5        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         A5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         B1        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         B2        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         A3        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         C1        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         C3        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         G1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         D1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         D5        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         E4        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         C8        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         A6        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    Fog_ENY        OUTPUT         A2        LVCMOS33           8            N/A            OREG       
    Fog_ENZ        OUTPUT        N11        LVCMOS33           8            N/A            OREG       
    Fog_INT        OUTPUT        A10        LVCMOS33           8            N/A            OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        F10        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         P6        LVCMOS33           8            N/A            OREG       
    KSG_SCL        OUTPUT        T14        LVCMOS33           8            NONE           NONE       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         G5        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        T15        LVCMOS33           8           PULLUP          NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |14590  |6975    |1412    |12186   |23      |0       |
|  AnyFog_dataX                      |AnyFog          |208    |71      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |54      |22      |52      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |71      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |52      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |210    |113     |22      |173     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |88     |66      |22      |51      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |3031   |697     |39      |2947    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |62     |34      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |206    |94      |5       |197     |0       |0       |
|    STADOP_com2                     |STADOP          |563    |89      |0       |555     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |42      |0       |0       |
|    head_com2                       |uniheading      |260    |50      |5       |251     |0       |0       |
|    rmc_com2                        |Gprmc           |159    |76      |0       |150     |0       |0       |
|    uart_com2                       |Agrica          |1414   |284     |10      |1398    |0       |0       |
|  DATA                              |Data_Processing |8683   |4401    |1065    |6967    |0       |0       |
|    DIV_Dtemp                       |Divider         |792    |326     |84      |657     |0       |0       |
|    DIV_Utemp                       |Divider         |613    |333     |84      |489     |0       |0       |
|    DIV_accX                        |Divider         |587    |275     |84      |454     |0       |0       |
|    DIV_accY                        |Divider         |600    |334     |114     |423     |0       |0       |
|    DIV_accZ                        |Divider         |709    |374     |132     |503     |0       |0       |
|    DIV_rateX                       |Divider         |683    |367     |132     |476     |0       |0       |
|    DIV_rateY                       |Divider         |612    |384     |132     |404     |0       |0       |
|    DIV_rateZ                       |Divider         |626    |399     |132     |412     |0       |0       |
|    genclk                          |genclk          |82     |55      |20      |49      |0       |0       |
|  FMC                               |FMC_Ctrl        |446    |391     |43      |345     |0       |0       |
|  IIC                               |I2C_master      |324    |261     |11      |274     |0       |0       |
|  IMU_CTRL                          |SCHA634         |909    |640     |61      |745     |0       |0       |
|    CtrlData                        |CtrlData        |457    |403     |47      |339     |0       |0       |
|      usms                          |Time_1ms        |28     |22      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |452    |237     |14      |406     |0       |0       |
|  POWER                             |POWER_EN        |100    |49      |38      |39      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |453    |279     |89      |287     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |453    |279     |89      |287     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |179    |113     |0       |165     |0       |0       |
|        reg_inst                    |register        |176    |110     |0       |162     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |274    |166     |89      |122     |0       |0       |
|        bus_inst                    |bus_top         |73     |45      |28      |25      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |26     |16      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |47     |29      |18      |15      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |123    |87      |29      |73      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13253  
    #2          2       3316   
    #3          3        600   
    #4          4        251   
    #5        5-10       905   
    #6        11-50      411   
    #7       51-100      11    
    #8       101-500      3    
    #9        >500        2    
  Average     2.09             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  1.946050s wall, 3.328125s user + 0.046875s system = 3.375000s CPU (173.4%)

RUN-1004 : used memory is 1008 MB, reserved memory is 1006 MB, peak memory is 1106 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 64951, tnet num: 18815, tinst num: 7894, tnode num: 88374, tedge num: 106959.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.637791s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.2%)

RUN-1004 : used memory is 1010 MB, reserved memory is 1008 MB, peak memory is 1106 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 18815 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.391131s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (100.0%)

RUN-1004 : used memory is 1021 MB, reserved memory is 1017 MB, peak memory is 1106 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 7894
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 18817, pip num: 139594
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 271
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3238 valid insts, and 391632 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001010010110001101
BIT-1004 : PLL setting string = 1000
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.128232s wall, 122.453125s user + 0.109375s system = 122.562500s CPU (1010.6%)

RUN-1004 : used memory is 1161 MB, reserved memory is 1145 MB, peak memory is 1275 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250711_083321.log"

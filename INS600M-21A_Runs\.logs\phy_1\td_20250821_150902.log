============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Aug 21 15:09:02 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Sys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(526)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(149)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(150)
HDL-1007 : undeclared symbol 'GPRMC_latch', assumed default net type 'wire' in ../../Src/UART/COM2_Control.v(152)
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  6.084519s wall, 1.828125s user + 4.250000s system = 6.078125s CPU (99.9%)

RUN-1004 : used memory is 79 MB, reserved memory is 40 MB, peak memory is 90 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.944041s wall, 1.859375s user + 0.078125s system = 1.937500s CPU (99.7%)

RUN-1004 : used memory is 302 MB, reserved memory is 270 MB, peak memory is 305 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 42 trigger nets, 42 data nets.
KIT-1004 : Chipwatcher code = 1000110011001111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=126) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=126)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=42,BUS_CTRL_NUM=104,BUS_WIDTH='{32'sb01000,32'sb01,32'sb010000,32'sb01,32'sb010000},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb01001,32'sb011001,32'sb011010},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb011010,32'sb0111110,32'sb01000100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 22786/23 useful/useless nets, 19536/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 22454/20 useful/useless nets, 19962/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 398 better
SYN-1014 : Optimize round 2
SYN-1032 : 22134/45 useful/useless nets, 19642/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.673546s wall, 2.593750s user + 0.093750s system = 2.687500s CPU (100.5%)

RUN-1004 : used memory is 329 MB, reserved memory is 295 MB, peak memory is 330 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 22182/299 useful/useless nets, 19727/47 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 391 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 40 instances.
SYN-2501 : Optimize round 1, 82 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 22630/5 useful/useless nets, 20175/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 82569, tnet num: 22630, tinst num: 20174, tnode num: 115645, tedge num: 129091.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.338265s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (99.2%)

RUN-1004 : used memory is 469 MB, reserved memory is 437 MB, peak memory is 469 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22630 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 237 (3.41), #lev = 7 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 534 instances into 237 LUTs, name keeping = 73%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 407 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 128 adder to BLE ...
SYN-4008 : Packed 128 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  5.107606s wall, 5.031250s user + 0.062500s system = 5.093750s CPU (99.7%)

RUN-1004 : used memory is 353 MB, reserved memory is 319 MB, peak memory is 576 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  8.137672s wall, 7.984375s user + 0.156250s system = 8.140625s CPU (100.0%)

RUN-1004 : used memory is 353 MB, reserved memory is 320 MB, peak memory is 576 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_end will be merged to another kept net COM2/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM2/rmc_com2/GPRMC_sta will be merged to another kept net COM2/GNRMC/GPRMC_sta
SYN-5055 WARNING: The kept net COM2/GNRMC/GNRMC_enddy[1] will be merged to another kept net COM2/GNRMC/GPRMC_stady[1]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[7] will be merged to another kept net COM2/GNRMC/fifo_di[7]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[6] will be merged to another kept net COM2/GNRMC/fifo_di[6]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[5] will be merged to another kept net COM2/GNRMC/fifo_di[5]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[4] will be merged to another kept net COM2/GNRMC/fifo_di[4]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[3] will be merged to another kept net COM2/GNRMC/fifo_di[3]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[2] will be merged to another kept net COM2/GNRMC/fifo_di[2]
SYN-5055 WARNING: The kept net COM2/GNRMC/u_fifo/di[1] will be merged to another kept net COM2/GNRMC/fifo_di[1]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (271 clock/control pins, 0 other pins).
SYN-4027 : Net AnyFog_dataX/UART_RX_COM3/clk is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net AnyFog_dataX/UART_RX_COM3/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 19418 instances
RUN-0007 : 5613 luts, 12199 seqs, 983 mslices, 519 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 21881 nets
RUN-1001 : 16406 nets have 2 pins
RUN-1001 : 4292 nets have [3 - 5] pins
RUN-1001 : 806 nets have [6 - 10] pins
RUN-1001 : 251 nets have [11 - 20] pins
RUN-1001 : 103 nets have [21 - 99] pins
RUN-1001 : 23 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4783     
RUN-1001 :   No   |  No   |  Yes  |     704     
RUN-1001 :   No   |  Yes  |  No   |     111     
RUN-1001 :   Yes  |  No   |  No   |    6083     
RUN-1001 :   Yes  |  No   |  Yes  |     443     
RUN-1001 :   Yes  |  Yes  |  No   |     75      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  115  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 123
PHY-3001 : Initial placement ...
PHY-3001 : design contains 19416 instances, 5613 luts, 12199 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 62%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80954, tnet num: 21879, tinst num: 19416, tnode num: 113821, tedge num: 127423.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.293943s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (100.2%)

RUN-1004 : used memory is 527 MB, reserved memory is 498 MB, peak memory is 576 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.254900s wall, 2.187500s user + 0.062500s system = 2.250000s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.64269e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 19416.
PHY-3001 : Level 1 #clusters 2143.
PHY-3001 : End clustering;  0.165680s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (160.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 62%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 897552, overlap = 628.375
PHY-3002 : Step(2): len = 812112, overlap = 695.094
PHY-3002 : Step(3): len = 527750, overlap = 909.312
PHY-3002 : Step(4): len = 461706, overlap = 977.625
PHY-3002 : Step(5): len = 360335, overlap = 1099.69
PHY-3002 : Step(6): len = 319641, overlap = 1156.66
PHY-3002 : Step(7): len = 274794, overlap = 1222.59
PHY-3002 : Step(8): len = 247443, overlap = 1248.34
PHY-3002 : Step(9): len = 213817, overlap = 1301.88
PHY-3002 : Step(10): len = 200150, overlap = 1346.22
PHY-3002 : Step(11): len = 179026, overlap = 1374.97
PHY-3002 : Step(12): len = 166841, overlap = 1394.84
PHY-3002 : Step(13): len = 151814, overlap = 1401.78
PHY-3002 : Step(14): len = 137142, overlap = 1450.66
PHY-3002 : Step(15): len = 127567, overlap = 1478.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.15389e-06
PHY-3002 : Step(16): len = 135799, overlap = 1453.56
PHY-3002 : Step(17): len = 183328, overlap = 1355.53
PHY-3002 : Step(18): len = 195757, overlap = 1191.03
PHY-3002 : Step(19): len = 198526, overlap = 1125.75
PHY-3002 : Step(20): len = 193251, overlap = 1086.78
PHY-3002 : Step(21): len = 188531, overlap = 1077.62
PHY-3002 : Step(22): len = 182074, overlap = 1086.28
PHY-3002 : Step(23): len = 177813, overlap = 1072.19
PHY-3002 : Step(24): len = 174637, overlap = 1071.06
PHY-3002 : Step(25): len = 171885, overlap = 1056.72
PHY-3002 : Step(26): len = 169419, overlap = 1071.44
PHY-3002 : Step(27): len = 167917, overlap = 1085.75
PHY-3002 : Step(28): len = 168480, overlap = 1087.38
PHY-3002 : Step(29): len = 168359, overlap = 1081.25
PHY-3002 : Step(30): len = 168207, overlap = 1096.16
PHY-3002 : Step(31): len = 166609, overlap = 1086.28
PHY-3002 : Step(32): len = 165348, overlap = 1082.03
PHY-3002 : Step(33): len = 164671, overlap = 1091.06
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.30777e-06
PHY-3002 : Step(34): len = 171970, overlap = 1068.72
PHY-3002 : Step(35): len = 186088, overlap = 978.438
PHY-3002 : Step(36): len = 189899, overlap = 938.75
PHY-3002 : Step(37): len = 192715, overlap = 906.281
PHY-3002 : Step(38): len = 193865, overlap = 905.938
PHY-3002 : Step(39): len = 193210, overlap = 910.062
PHY-3002 : Step(40): len = 192121, overlap = 914.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.61554e-06
PHY-3002 : Step(41): len = 202111, overlap = 872.062
PHY-3002 : Step(42): len = 217585, overlap = 798.781
PHY-3002 : Step(43): len = 224300, overlap = 778.656
PHY-3002 : Step(44): len = 227442, overlap = 768.125
PHY-3002 : Step(45): len = 226448, overlap = 777.25
PHY-3002 : Step(46): len = 225021, overlap = 774.656
PHY-3002 : Step(47): len = 222880, overlap = 764.781
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 9.23108e-06
PHY-3002 : Step(48): len = 236685, overlap = 727.875
PHY-3002 : Step(49): len = 250020, overlap = 632.906
PHY-3002 : Step(50): len = 255294, overlap = 571.688
PHY-3002 : Step(51): len = 258015, overlap = 555.656
PHY-3002 : Step(52): len = 256673, overlap = 557.312
PHY-3002 : Step(53): len = 255630, overlap = 539.625
PHY-3002 : Step(54): len = 253536, overlap = 548.031
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.84622e-05
PHY-3002 : Step(55): len = 266551, overlap = 502.5
PHY-3002 : Step(56): len = 281218, overlap = 462.094
PHY-3002 : Step(57): len = 286741, overlap = 455.656
PHY-3002 : Step(58): len = 287808, overlap = 459.375
PHY-3002 : Step(59): len = 286832, overlap = 448.25
PHY-3002 : Step(60): len = 284951, overlap = 462
PHY-3002 : Step(61): len = 283368, overlap = 463.688
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.69243e-05
PHY-3002 : Step(62): len = 294480, overlap = 430.25
PHY-3002 : Step(63): len = 305168, overlap = 405.25
PHY-3002 : Step(64): len = 309210, overlap = 400.125
PHY-3002 : Step(65): len = 310574, overlap = 388.594
PHY-3002 : Step(66): len = 309090, overlap = 377.625
PHY-3002 : Step(67): len = 307063, overlap = 374.562
PHY-3002 : Step(68): len = 304687, overlap = 383.375
PHY-3002 : Step(69): len = 304400, overlap = 373.125
PHY-3002 : Step(70): len = 304259, overlap = 386.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.38487e-05
PHY-3002 : Step(71): len = 311434, overlap = 362.094
PHY-3002 : Step(72): len = 319858, overlap = 342.281
PHY-3002 : Step(73): len = 323886, overlap = 321.5
PHY-3002 : Step(74): len = 324752, overlap = 315.969
PHY-3002 : Step(75): len = 323716, overlap = 310.656
PHY-3002 : Step(76): len = 322128, overlap = 314.719
PHY-3002 : Step(77): len = 319841, overlap = 320.656
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000144934
PHY-3002 : Step(78): len = 324325, overlap = 319.562
PHY-3002 : Step(79): len = 331128, overlap = 311.406
PHY-3002 : Step(80): len = 333443, overlap = 285.062
PHY-3002 : Step(81): len = 334086, overlap = 281.094
PHY-3002 : Step(82): len = 333669, overlap = 269.281
PHY-3002 : Step(83): len = 332750, overlap = 277.969
PHY-3002 : Step(84): len = 332199, overlap = 293.906
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(85): len = 335003, overlap = 294.188
PHY-3002 : Step(86): len = 338341, overlap = 295.688
PHY-3002 : Step(87): len = 339845, overlap = 296.406
PHY-3002 : Step(88): len = 341950, overlap = 292.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.018355s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 443936, over cnt = 1190(3%), over = 5166, worst = 37
PHY-1001 : End global iterations;  0.864097s wall, 1.156250s user + 0.062500s system = 1.218750s CPU (141.0%)

PHY-1001 : Congestion index: top1 = 74.96, top5 = 52.84, top10 = 43.47, top15 = 37.85.
PHY-3001 : End congestion estimation;  1.123418s wall, 1.390625s user + 0.078125s system = 1.468750s CPU (130.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.013903s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.63913e-05
PHY-3002 : Step(89): len = 385791, overlap = 215.188
PHY-3002 : Step(90): len = 397338, overlap = 181.656
PHY-3002 : Step(91): len = 399537, overlap = 169.031
PHY-3002 : Step(92): len = 400009, overlap = 156.625
PHY-3002 : Step(93): len = 406443, overlap = 135.688
PHY-3002 : Step(94): len = 412199, overlap = 125.094
PHY-3002 : Step(95): len = 417756, overlap = 123.031
PHY-3002 : Step(96): len = 423280, overlap = 120.75
PHY-3002 : Step(97): len = 425972, overlap = 119.844
PHY-3002 : Step(98): len = 428285, overlap = 122.438
PHY-3002 : Step(99): len = 431769, overlap = 120.656
PHY-3002 : Step(100): len = 433516, overlap = 119.344
PHY-3002 : Step(101): len = 435355, overlap = 119.719
PHY-3002 : Step(102): len = 439028, overlap = 121.188
PHY-3002 : Step(103): len = 439698, overlap = 117.906
PHY-3002 : Step(104): len = 440103, overlap = 116.344
PHY-3002 : Step(105): len = 442205, overlap = 117.469
PHY-3002 : Step(106): len = 443276, overlap = 118.844
PHY-3002 : Step(107): len = 443919, overlap = 122.469
PHY-3002 : Step(108): len = 444597, overlap = 122.844
PHY-3002 : Step(109): len = 444593, overlap = 124.656
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000192783
PHY-3002 : Step(110): len = 445948, overlap = 121.469
PHY-3002 : Step(111): len = 447229, overlap = 121.156
PHY-3002 : Step(112): len = 447824, overlap = 120.781
PHY-3002 : Step(113): len = 451713, overlap = 114.062
PHY-3002 : Step(114): len = 454868, overlap = 110.344
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000385565
PHY-3002 : Step(115): len = 453803, overlap = 103.688
PHY-3002 : Step(116): len = 460697, overlap = 102.156
PHY-3002 : Step(117): len = 473215, overlap = 86.8125
PHY-3002 : Step(118): len = 473033, overlap = 86.7812
PHY-3002 : Step(119): len = 471254, overlap = 88.3125
PHY-3002 : Step(120): len = 470706, overlap = 83.2188
PHY-3002 : Step(121): len = 471091, overlap = 77.7188
PHY-3002 : Step(122): len = 472819, overlap = 73.5625
PHY-3002 : Step(123): len = 475930, overlap = 79.3438
PHY-3002 : Step(124): len = 474761, overlap = 79.7812
PHY-3002 : Step(125): len = 473971, overlap = 82.5312
PHY-3002 : Step(126): len = 473532, overlap = 87.8125
PHY-3002 : Step(127): len = 474702, overlap = 92.2812
PHY-3002 : Step(128): len = 474034, overlap = 97.1562
PHY-3002 : Step(129): len = 472393, overlap = 98.2188
PHY-3002 : Step(130): len = 470404, overlap = 99.8438
PHY-3002 : Step(131): len = 470934, overlap = 102.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00077113
PHY-3002 : Step(132): len = 470840, overlap = 107.531
PHY-3002 : Step(133): len = 474114, overlap = 109.219
PHY-3002 : Step(134): len = 477557, overlap = 108.031
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00147269
PHY-3002 : Step(135): len = 477352, overlap = 104.938
PHY-3002 : Step(136): len = 482578, overlap = 101.25
PHY-3002 : Step(137): len = 495563, overlap = 102.688
PHY-3002 : Step(138): len = 500031, overlap = 105.906
PHY-3002 : Step(139): len = 501823, overlap = 111.344
PHY-3002 : Step(140): len = 501680, overlap = 105.531
PHY-3002 : Step(141): len = 502677, overlap = 105.344
PHY-3002 : Step(142): len = 502364, overlap = 107.281
PHY-3002 : Step(143): len = 501751, overlap = 106.938
PHY-3002 : Step(144): len = 500919, overlap = 100.812
PHY-3002 : Step(145): len = 500602, overlap = 99.7812
PHY-3002 : Step(146): len = 500312, overlap = 98
PHY-3002 : Step(147): len = 499286, overlap = 84.7188
PHY-3002 : Step(148): len = 497940, overlap = 88.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 561632, over cnt = 2230(6%), over = 11111, worst = 48
PHY-1001 : End global iterations;  1.099688s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (169.1%)

PHY-1001 : Congestion index: top1 = 92.07, top5 = 66.29, top10 = 55.67, top15 = 49.65.
PHY-3001 : End congestion estimation;  1.446441s wall, 2.187500s user + 0.015625s system = 2.203125s CPU (152.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.056922s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.83581e-05
PHY-3002 : Step(149): len = 505111, overlap = 366.375
PHY-3002 : Step(150): len = 506234, overlap = 302.406
PHY-3002 : Step(151): len = 499846, overlap = 279.312
PHY-3002 : Step(152): len = 495288, overlap = 264.906
PHY-3002 : Step(153): len = 492155, overlap = 236.562
PHY-3002 : Step(154): len = 489562, overlap = 227.188
PHY-3002 : Step(155): len = 486188, overlap = 224.438
PHY-3002 : Step(156): len = 483471, overlap = 213.562
PHY-3002 : Step(157): len = 481945, overlap = 201.906
PHY-3002 : Step(158): len = 477692, overlap = 201.281
PHY-3002 : Step(159): len = 474431, overlap = 219.25
PHY-3002 : Step(160): len = 471667, overlap = 225.531
PHY-3002 : Step(161): len = 467749, overlap = 218.938
PHY-3002 : Step(162): len = 465573, overlap = 211.062
PHY-3002 : Step(163): len = 462324, overlap = 212.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000196716
PHY-3002 : Step(164): len = 461655, overlap = 210.906
PHY-3002 : Step(165): len = 462939, overlap = 208.062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000393432
PHY-3002 : Step(166): len = 464849, overlap = 193.5
PHY-3002 : Step(167): len = 472123, overlap = 175.344
PHY-3002 : Step(168): len = 475793, overlap = 160.219
PHY-3002 : Step(169): len = 475598, overlap = 156.375
PHY-3002 : Step(170): len = 476314, overlap = 148.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000786865
PHY-3002 : Step(171): len = 477391, overlap = 142.688
PHY-3002 : Step(172): len = 480384, overlap = 136.375
PHY-3002 : Step(173): len = 484017, overlap = 124.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00143856
PHY-3002 : Step(174): len = 484818, overlap = 120.562
PHY-3002 : Step(175): len = 489579, overlap = 111.469
PHY-3002 : Step(176): len = 495269, overlap = 103.719
PHY-3002 : Step(177): len = 496518, overlap = 101.25
PHY-3002 : Step(178): len = 496358, overlap = 104.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 80954, tnet num: 21879, tinst num: 19416, tnode num: 113821, tedge num: 127423.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.716970s wall, 1.671875s user + 0.031250s system = 1.703125s CPU (99.2%)

RUN-1004 : used memory is 568 MB, reserved memory is 543 MB, peak memory is 701 MB
OPT-1001 : Total overflow 449.69 peak overflow 2.88
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 346/21881.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 579624, over cnt = 2578(7%), over = 8818, worst = 22
PHY-1001 : End global iterations;  1.457501s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (145.8%)

PHY-1001 : Congestion index: top1 = 58.90, top5 = 48.58, top10 = 43.59, top15 = 40.48.
PHY-1001 : End incremental global routing;  1.833064s wall, 2.500000s user + 0.000000s system = 2.500000s CPU (136.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 21879 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.116477s wall, 1.031250s user + 0.078125s system = 1.109375s CPU (99.4%)

OPT-1001 : 19 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 19335 has valid locations, 253 needs to be replaced
PHY-3001 : design contains 19650 instances, 5712 luts, 12334 seqs, 1502 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 512648
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17789/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592616, over cnt = 2611(7%), over = 8894, worst = 22
PHY-1001 : End global iterations;  0.219649s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (149.4%)

PHY-1001 : Congestion index: top1 = 59.44, top5 = 48.97, top10 = 43.94, top15 = 40.88.
PHY-3001 : End congestion estimation;  0.494081s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (123.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81727, tnet num: 22113, tinst num: 19650, tnode num: 114913, tedge num: 128501.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.696122s wall, 1.625000s user + 0.062500s system = 1.687500s CPU (99.5%)

RUN-1004 : used memory is 614 MB, reserved memory is 606 MB, peak memory is 705 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.864761s wall, 2.765625s user + 0.093750s system = 2.859375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(179): len = 512786, overlap = 0.125
PHY-3002 : Step(180): len = 513997, overlap = 0.125
PHY-3002 : Step(181): len = 514635, overlap = 0.125
PHY-3002 : Step(182): len = 515081, overlap = 0.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 69%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17826/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591736, over cnt = 2614(7%), over = 8907, worst = 22
PHY-1001 : End global iterations;  0.207666s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.8%)

PHY-1001 : Congestion index: top1 = 59.59, top5 = 49.15, top10 = 44.12, top15 = 41.00.
PHY-3001 : End congestion estimation;  0.498937s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (97.1%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.270048s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000514694
PHY-3002 : Step(183): len = 515136, overlap = 107.688
PHY-3002 : Step(184): len = 515392, overlap = 107.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00102939
PHY-3002 : Step(185): len = 515683, overlap = 107.25
PHY-3002 : Step(186): len = 516123, overlap = 107.281
PHY-3001 : Final: Len = 516123, Over = 107.281
PHY-3001 : End incremental placement;  6.166163s wall, 6.359375s user + 0.250000s system = 6.609375s CPU (107.2%)

OPT-1001 : Total overflow 455.88 peak overflow 2.88
OPT-1001 : End high-fanout net optimization;  9.725212s wall, 10.640625s user + 0.328125s system = 10.968750s CPU (112.8%)

OPT-1001 : Current memory(MB): used = 709, reserve = 689, peak = 725.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17844/22115.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 594520, over cnt = 2590(7%), over = 8458, worst = 22
PHY-1002 : len = 631288, over cnt = 1863(5%), over = 4771, worst = 22
PHY-1002 : len = 661736, over cnt = 977(2%), over = 2330, worst = 16
PHY-1002 : len = 672544, over cnt = 654(1%), over = 1550, worst = 14
PHY-1002 : len = 694280, over cnt = 124(0%), over = 282, worst = 11
PHY-1001 : End global iterations;  1.363949s wall, 1.937500s user + 0.015625s system = 1.953125s CPU (143.2%)

PHY-1001 : Congestion index: top1 = 50.60, top5 = 44.36, top10 = 41.10, top15 = 38.96.
OPT-1001 : End congestion update;  1.760650s wall, 2.343750s user + 0.015625s system = 2.359375s CPU (134.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22113 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.939817s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.8%)

OPT-0007 : Start: WNS 3769 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.706231s wall, 3.281250s user + 0.015625s system = 3.296875s CPU (121.8%)

OPT-1001 : Current memory(MB): used = 686, reserve = 672, peak = 725.
OPT-1001 : End physical optimization;  14.492950s wall, 16.093750s user + 0.390625s system = 16.484375s CPU (113.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 5712 LUT to BLE ...
SYN-4008 : Packed 5712 LUT and 2728 SEQ to BLE.
SYN-4003 : Packing 9606 remaining SEQ's ...
SYN-4005 : Packed 3395 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 6211 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 11923/13793 primitive instances ...
PHY-3001 : End packing;  3.084025s wall, 3.078125s user + 0.000000s system = 3.078125s CPU (99.8%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 8109 instances
RUN-1001 : 4003 mslices, 4002 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19438 nets
RUN-1001 : 13610 nets have 2 pins
RUN-1001 : 4415 nets have [3 - 5] pins
RUN-1001 : 874 nets have [6 - 10] pins
RUN-1001 : 406 nets have [11 - 20] pins
RUN-1001 : 124 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
PHY-3001 : design contains 8107 instances, 8005 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Cell area utilization is 85%
PHY-3001 : After packing: Len = 529691, Over = 345
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8010/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 658736, over cnt = 1555(4%), over = 2411, worst = 9
PHY-1002 : len = 665640, over cnt = 936(2%), over = 1249, worst = 6
PHY-1002 : len = 674544, over cnt = 386(1%), over = 489, worst = 6
PHY-1002 : len = 679096, over cnt = 161(0%), over = 202, worst = 4
PHY-1002 : len = 682648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.359825s wall, 2.140625s user + 0.031250s system = 2.171875s CPU (159.7%)

PHY-1001 : Congestion index: top1 = 49.74, top5 = 43.54, top10 = 40.36, top15 = 38.28.
PHY-3001 : End congestion estimation;  1.712397s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (146.9%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67825, tnet num: 19436, tinst num: 8107, tnode num: 91942, tedge num: 111857.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.830241s wall, 1.781250s user + 0.046875s system = 1.828125s CPU (99.9%)

RUN-1004 : used memory is 603 MB, reserved memory is 593 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.837240s wall, 2.796875s user + 0.046875s system = 2.843750s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.48336e-05
PHY-3002 : Step(187): len = 535591, overlap = 325.25
PHY-3002 : Step(188): len = 534117, overlap = 332.25
PHY-3002 : Step(189): len = 532772, overlap = 348.25
PHY-3002 : Step(190): len = 532114, overlap = 355.5
PHY-3002 : Step(191): len = 528956, overlap = 359.25
PHY-3002 : Step(192): len = 526910, overlap = 352.25
PHY-3002 : Step(193): len = 524925, overlap = 360.75
PHY-3002 : Step(194): len = 522979, overlap = 366
PHY-3002 : Step(195): len = 521081, overlap = 373.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109667
PHY-3002 : Step(196): len = 524688, overlap = 360.5
PHY-3002 : Step(197): len = 528452, overlap = 349.5
PHY-3002 : Step(198): len = 528409, overlap = 342.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000219334
PHY-3002 : Step(199): len = 534084, overlap = 333.75
PHY-3002 : Step(200): len = 544291, overlap = 318.5
PHY-3002 : Step(201): len = 545995, overlap = 318.75
PHY-3002 : Step(202): len = 544288, overlap = 312.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.744055s wall, 0.921875s user + 0.750000s system = 1.671875s CPU (224.7%)

PHY-3001 : Trial Legalized: Len = 642176
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 614/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 731640, over cnt = 2406(6%), over = 3879, worst = 8
PHY-1002 : len = 745768, over cnt = 1489(4%), over = 2043, worst = 7
PHY-1002 : len = 764280, over cnt = 543(1%), over = 686, worst = 7
PHY-1002 : len = 770264, over cnt = 252(0%), over = 339, worst = 7
PHY-1002 : len = 777112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.225445s wall, 3.437500s user + 0.062500s system = 3.500000s CPU (157.3%)

PHY-1001 : Congestion index: top1 = 52.11, top5 = 46.26, top10 = 43.01, top15 = 41.04.
PHY-3001 : End congestion estimation;  2.629580s wall, 3.843750s user + 0.062500s system = 3.906250s CPU (148.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.997586s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00018368
PHY-3002 : Step(203): len = 601940, overlap = 75.25
PHY-3002 : Step(204): len = 584009, overlap = 112
PHY-3002 : Step(205): len = 572274, overlap = 161.75
PHY-3002 : Step(206): len = 565447, overlap = 204.5
PHY-3002 : Step(207): len = 561443, overlap = 225.75
PHY-3002 : Step(208): len = 559329, overlap = 242
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000367359
PHY-3002 : Step(209): len = 563474, overlap = 235.75
PHY-3002 : Step(210): len = 567507, overlap = 232.75
PHY-3002 : Step(211): len = 567611, overlap = 231.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(212): len = 569924, overlap = 230.25
PHY-3002 : Step(213): len = 576143, overlap = 225.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.053248s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.0%)

PHY-3001 : Legalized: Len = 617743, Over = 0
PHY-3001 : Spreading special nets. 51 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.091073s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.9%)

PHY-3001 : 78 instances has been re-located, deltaX = 28, deltaY = 39, maxDist = 2.
PHY-3001 : Final: Len = 618977, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67825, tnet num: 19436, tinst num: 8107, tnode num: 91942, tedge num: 111857.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.258968s wall, 2.250000s user + 0.015625s system = 2.265625s CPU (100.3%)

RUN-1004 : used memory is 620 MB, reserved memory is 621 MB, peak memory is 725 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3993/19438.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718976, over cnt = 2167(6%), over = 3336, worst = 8
PHY-1002 : len = 729616, over cnt = 1312(3%), over = 1780, worst = 6
PHY-1002 : len = 747736, over cnt = 258(0%), over = 335, worst = 5
PHY-1002 : len = 752040, over cnt = 73(0%), over = 94, worst = 5
PHY-1002 : len = 754096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.840181s wall, 2.921875s user + 0.031250s system = 2.953125s CPU (160.5%)

PHY-1001 : Congestion index: top1 = 50.39, top5 = 44.41, top10 = 41.39, top15 = 39.56.
PHY-1001 : End incremental global routing;  2.179907s wall, 3.265625s user + 0.031250s system = 3.296875s CPU (151.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19436 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.001925s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 59 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 8044 has valid locations, 10 needs to be replaced
PHY-3001 : design contains 8116 instances, 8014 slices, 291 macros(1502 instances: 983 mslices 519 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 622460
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17659/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 759064, over cnt = 21(0%), over = 25, worst = 2
PHY-1002 : len = 759128, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 759200, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 759312, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 759400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.742598s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (105.2%)

PHY-1001 : Congestion index: top1 = 50.62, top5 = 44.60, top10 = 41.50, top15 = 39.66.
PHY-3001 : End congestion estimation;  1.069584s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (102.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67896, tnet num: 19444, tinst num: 8116, tnode num: 92022, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.087213s wall, 2.093750s user + 0.000000s system = 2.093750s CPU (100.3%)

RUN-1004 : used memory is 651 MB, reserved memory is 638 MB, peak memory is 725 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.122236s wall, 3.109375s user + 0.015625s system = 3.125000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(214): len = 622005, overlap = 0
PHY-3002 : Step(215): len = 621751, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 85%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 17656/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757080, over cnt = 26(0%), over = 48, worst = 5
PHY-1002 : len = 757264, over cnt = 22(0%), over = 29, worst = 3
PHY-1002 : len = 757504, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 757520, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 757568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.753965s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (109.8%)

PHY-1001 : Congestion index: top1 = 50.73, top5 = 44.63, top10 = 41.60, top15 = 39.74.
PHY-3001 : End congestion estimation;  1.087889s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (104.8%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.999120s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00145661
PHY-3002 : Step(216): len = 621767, overlap = 0.25
PHY-3002 : Step(217): len = 621744, overlap = 1.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007522s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (207.7%)

PHY-3001 : Legalized: Len = 621738, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.075357s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (82.9%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 621730, Over = 0
PHY-3001 : End incremental placement;  6.961034s wall, 7.109375s user + 0.125000s system = 7.234375s CPU (103.9%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  10.681914s wall, 12.078125s user + 0.156250s system = 12.234375s CPU (114.5%)

OPT-1001 : Current memory(MB): used = 721, reserve = 707, peak = 727.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17657/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757488, over cnt = 15(0%), over = 24, worst = 6
PHY-1002 : len = 757432, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 757424, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 757496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.576138s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (103.1%)

PHY-1001 : Congestion index: top1 = 50.04, top5 = 44.43, top10 = 41.50, top15 = 39.69.
OPT-1001 : End congestion update;  0.899352s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (102.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.831217s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.6%)

OPT-0007 : Start: WNS 3829 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  1.735648s wall, 1.750000s user + 0.000000s system = 1.750000s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 721, reserve = 707, peak = 727.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.835072s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (99.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17668/19446.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 757496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126572s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 50.04, top5 = 44.43, top10 = 41.50, top15 = 39.69.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.832953s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3829 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 49.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3829ps with logic level 4 
OPT-1001 : End physical optimization;  17.071625s wall, 18.421875s user + 0.218750s system = 18.640625s CPU (109.2%)

RUN-1003 : finish command "place" in  81.412449s wall, 160.281250s user + 8.125000s system = 168.406250s CPU (206.9%)

RUN-1004 : used memory is 602 MB, reserved memory is 587 MB, peak memory is 727 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.749708s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (175.9%)

RUN-1004 : used memory is 602 MB, reserved memory is 588 MB, peak memory is 727 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 8118 instances
RUN-1001 : 4003 mslices, 4011 lslices, 59 pads, 40 brams, 0 dsps
RUN-1001 : There are total 19446 nets
RUN-1001 : 13609 nets have 2 pins
RUN-1001 : 4415 nets have [3 - 5] pins
RUN-1001 : 877 nets have [6 - 10] pins
RUN-1001 : 411 nets have [11 - 20] pins
RUN-1001 : 125 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67896, tnet num: 19444, tinst num: 8116, tnode num: 92022, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.852906s wall, 1.828125s user + 0.015625s system = 1.843750s CPU (99.5%)

RUN-1004 : used memory is 596 MB, reserved memory is 584 MB, peak memory is 727 MB
PHY-1001 : 4003 mslices, 4011 lslices, 59 pads, 40 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 698936, over cnt = 2300(6%), over = 3770, worst = 9
PHY-1002 : len = 715952, over cnt = 1239(3%), over = 1694, worst = 7
PHY-1002 : len = 729000, over cnt = 506(1%), over = 675, worst = 7
PHY-1002 : len = 740144, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 740528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.893476s wall, 3.296875s user + 0.062500s system = 3.359375s CPU (177.4%)

PHY-1001 : Congestion index: top1 = 49.68, top5 = 44.18, top10 = 41.00, top15 = 39.18.
PHY-1001 : End global routing;  2.262402s wall, 3.671875s user + 0.062500s system = 3.734375s CPU (165.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 701, reserve = 694, peak = 727.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net AnyFog_dataX/UART_RX_COM3/clk will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 972, reserve = 962, peak = 972.
PHY-1001 : End build detailed router design. 4.790874s wall, 4.750000s user + 0.046875s system = 4.796875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 192760, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.946713s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 1006, reserve = 997, peak = 1006.
PHY-1001 : End phase 1; 0.958209s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 55% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 76% nets.
PHY-1001 : Routed 93% nets.
PHY-1022 : len = 1.71225e+06, over cnt = 1279(0%), over = 1283, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1023, reserve = 1012, peak = 1023.
PHY-1001 : End initial routed; 17.780620s wall, 48.750000s user + 0.390625s system = 49.140625s CPU (276.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18174(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.202   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.649363s wall, 3.640625s user + 0.015625s system = 3.656250s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 1036, reserve = 1025, peak = 1036.
PHY-1001 : End phase 2; 21.430144s wall, 52.390625s user + 0.406250s system = 52.796875s CPU (246.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 1.71225e+06, over cnt = 1279(0%), over = 1283, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.259579s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 1.70052e+06, over cnt = 419(0%), over = 420, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 0.769701s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (196.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 1.70187e+06, over cnt = 96(0%), over = 96, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.381104s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (143.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 1.70274e+06, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.285458s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (125.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 1.70311e+06, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.186772s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/18174(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.126   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.667618s wall, 3.656250s user + 0.000000s system = 3.656250s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 274 feed throughs used by 245 nets
PHY-1001 : End commit to database; 2.356700s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1124, reserve = 1115, peak = 1124.
PHY-1001 : End phase 3; 8.447983s wall, 9.390625s user + 0.031250s system = 9.421875s CPU (111.5%)

PHY-1003 : Routed, final wirelength = 1.70311e+06
PHY-1001 : Current memory(MB): used = 1128, reserve = 1120, peak = 1128.
PHY-1001 : End export database. 0.065966s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.7%)

PHY-1001 : End detail routing;  36.145674s wall, 67.984375s user + 0.484375s system = 68.468750s CPU (189.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67896, tnet num: 19444, tinst num: 8116, tnode num: 92022, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.890332s wall, 1.890625s user + 0.000000s system = 1.890625s CPU (100.0%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1053 MB, peak memory is 1128 MB
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  44.967516s wall, 78.156250s user + 0.609375s system = 78.765625s CPU (175.2%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1054 MB, peak memory is 1128 MB
RUN-1002 : start command "report_area -io_info -file INS600M-21A_phy.area"
RUN-1001 : standard
***Report Model: INS600M_21A Device: EG4X20BG256***

IO Statistics
#IO                        66
  #input                   26
  #output                  38
  #inout                    2

Utilization Statistics
#lut                     8877   out of  19600   45.29%
#reg                    12428   out of  19600   63.41%
#le                     15033
  #lut only              2605   out of  15033   17.33%
  #reg only              6156   out of  15033   40.95%
  #lut&reg               6272   out of  15033   41.72%
#dsp                        0   out of     29    0.00%
#bram                      40   out of     64   62.50%
  #bram9k                  38
  #fifo9k                   2
#bram32k                    0   out of     16    0.00%
#pad                       59   out of    188   31.38%
  #ireg                     9
  #oreg                    33
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet                         Type               DriverType         Driver                    Fanout
#1        AnyFog_dataX/UART_RX_COM3/clk    GCLK               pll                CLK100M/pll_inst.clkc0    6798
#2        config_inst_syn_9                GCLK               config             config_inst.jtck          158
#3        clk_in_dup_1                     GeneralRouting     io                 clk_in_syn_2.di           1


Detailed IO Report

      Name        Direction    Location    IOStandard    DriveStrength    PullType       PackReg      
    FMC_NCE         INPUT         F2        LVCMOS33          N/A          PULLUP          IREG       
    FMC_NOE         INPUT         F4        LVCMOS33          N/A          PULLUP          NONE       
    FMC_NWE         INPUT         F3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[7]       INPUT         D1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[6]       INPUT         B3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[5]       INPUT         C1        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[4]       INPUT         C2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[3]       INPUT         A3        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[2]       INPUT         A2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[1]       INPUT         B2        LVCMOS33          N/A          PULLUP          NONE       
  FMC_addr[0]       INPUT         B1        LVCMOS33          N/A          PULLUP          NONE       
    Fog_RXDX        INPUT        F15        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDY        INPUT         H4        LVCMOS33          N/A          PULLUP          IREG       
    Fog_RXDZ        INPUT        K15        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_PPS        INPUT        K14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX2        INPUT        H14        LVCMOS33          N/A          PULLUP          IREG       
    GNSS_RX3        INPUT        J12        LVCMOS33          N/A          PULLUP          IREG       
   Version[3]       INPUT         R1        LVCMOS33          N/A          PULLUP          NONE       
   Version[2]       INPUT         R2        LVCMOS33          N/A          PULLUP          NONE       
   Version[1]       INPUT         P2        LVCMOS33          N/A          PULLUP          NONE       
   Version[0]       INPUT         P1        LVCMOS33          N/A          PULLUP          NONE       
     clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP          NONE       
      miso          INPUT        P15        LVCMOS33          N/A          PULLUP          IREG       
    ARM_INT        OUTPUT         H5        LVCMOS33           8            NONE           OREG       
    DACC_EN        OUTPUT        B16        LVCMOS33           8            N/A            OREG       
    DACC_INT       OUTPUT        B14        LVCMOS33           8            N/A            OREG       
  FMC_data[15]     OUTPUT         P6        LVCMOS33           8            NONE           OREG       
  FMC_data[14]     OUTPUT         N4        LVCMOS33           8            NONE           OREG       
  FMC_data[13]     OUTPUT         M5        LVCMOS33           8            NONE           OREG       
  FMC_data[12]     OUTPUT         N6        LVCMOS33           8            NONE           OREG       
  FMC_data[11]     OUTPUT         N5        LVCMOS33           8            NONE           OREG       
  FMC_data[10]     OUTPUT         P5        LVCMOS33           8            NONE           OREG       
  FMC_data[9]      OUTPUT         M3        LVCMOS33           8            NONE           OREG       
  FMC_data[8]      OUTPUT         M4        LVCMOS33           8            NONE           OREG       
  FMC_data[7]      OUTPUT         K1        LVCMOS33           8            NONE           OREG       
  FMC_data[6]      OUTPUT         J1        LVCMOS33           8            NONE           OREG       
  FMC_data[5]      OUTPUT         H2        LVCMOS33           8            NONE           OREG       
  FMC_data[4]      OUTPUT         M1        LVCMOS33           8            NONE           OREG       
  FMC_data[3]      OUTPUT         L1        LVCMOS33           8            NONE           OREG       
  FMC_data[2]      OUTPUT         N1        LVCMOS33           8            NONE           OREG       
  FMC_data[1]      OUTPUT         N3        LVCMOS33           8            NONE           OREG       
  FMC_data[0]      OUTPUT         M2        LVCMOS33           8            NONE           OREG       
    Fog_ENX        OUTPUT         T5        LVCMOS33           8            NONE           OREG       
    Fog_ENY        OUTPUT         T4        LVCMOS33           8            NONE           OREG       
    Fog_ENZ        OUTPUT         T7        LVCMOS33           8            NONE           OREG       
    Fog_INT        OUTPUT         R5        LVCMOS33           8            NONE           OREG       
    GNSS_EN        OUTPUT        A13        LVCMOS33           8            NONE           OREG       
    GNSS_LED       OUTPUT        C15        LVCMOS33           8            NONE           OREG       
    I2C_SCL        OUTPUT        D16        LVCMOS33           8            N/A            OREG       
    IMU_INT        OUTPUT         T6        LVCMOS33           8            N/A            OREG       
    TXD_PPS        OUTPUT        L13        LVCMOS33           8            NONE           NONE       
    TXD_RMC        OUTPUT         L3        LVCMOS33           8            NONE           OREG       
     cs_due        OUTPUT        M16        LVCMOS33           8            NONE           OREG       
     cs_uno        OUTPUT        N16        LVCMOS33           8            NONE           OREG       
      mosi         OUTPUT        L16        LVCMOS33           8            NONE           OREG       
      sclk         OUTPUT         K3        LVCMOS33           8            NONE           OREG       
    spi_rst        OUTPUT        P16        LVCMOS33           8            NONE           NONE       
    I2C_SDA         INOUT         E1        LVCMOS33           8            N/A       IREG;OREG;TREG  
    KSG_SDA         INOUT        G11        LVCMOS25           8            N/A            NONE       

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |INS600M_21A     |15033  |7375    |1502    |12471   |40      |0       |
|  AnyFog_dataX                      |AnyFog          |209    |74      |22      |174     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |55      |22      |51      |0       |0       |
|  AnyFog_dataY                      |AnyFog          |208    |69      |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |86     |53      |22      |48      |0       |0       |
|  AnyFog_dataZ                      |AnyFog          |204    |109     |22      |170     |0       |0       |
|    UART_RX_COM3                    |UART_RX115200E  |84     |61      |22      |50      |0       |0       |
|  CLK100M                           |global_clock    |0      |0       |0       |0       |0       |0       |
|  COM2                              |COM2_Control    |2913   |617     |39      |2827    |1       |0       |
|    GNRMC                           |GNRMC_Tx        |63     |40      |5       |54      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    PPPNAV_com2                     |PPPNAV          |227    |89      |5       |212     |0       |0       |
|    STADOP_com2                     |STADOP          |550    |79      |0       |545     |0       |0       |
|    UART_RX_COM3                    |UART_RX460800   |65     |47      |14      |43      |0       |0       |
|    head_com2                       |uniheading      |259    |104     |5       |246     |0       |0       |
|    rmc_com2                        |Gprmc           |31     |31      |0       |27      |0       |0       |
|    uart_com2                       |Agrica          |1417   |212     |10      |1399    |0       |0       |
|  COM3                              |COM3_Control    |279    |140     |19      |237     |1       |0       |
|    GNRMC                           |GNRMC_Tx        |60     |33      |5       |52      |1       |0       |
|      u_fifo                        |Sys_fifo8x8     |0      |0       |0       |0       |1       |0       |
|    UART_RX_COM3                    |UART_RX460800   |61     |38      |14      |38      |0       |0       |
|    rmc_com3                        |Gprmc           |158    |69      |0       |147     |0       |0       |
|  DATA                              |Data_Processing |8833   |4592    |1122    |7040    |0       |0       |
|    DIV_Dtemp                       |Divider         |781    |324     |84      |654     |0       |0       |
|    DIV_Utemp                       |Divider         |670    |274     |84      |546     |0       |0       |
|    DIV_accX                        |Divider         |629    |315     |84      |508     |0       |0       |
|    DIV_accY                        |Divider         |587    |335     |102     |433     |0       |0       |
|    DIV_accZ                        |Divider         |683    |420     |132     |477     |0       |0       |
|    DIV_rateX                       |Divider         |635    |408     |132     |429     |0       |0       |
|    DIV_rateY                       |Divider         |622    |426     |132     |396     |0       |0       |
|    DIV_rateZ                       |Divider         |567    |362     |132     |360     |0       |0       |
|    genclk                          |genclk          |260    |159     |89      |101     |0       |0       |
|  FMC                               |FMC_Ctrl        |513    |461     |43      |367     |0       |0       |
|  IIC                               |I2C_master      |310    |260     |11      |265     |0       |0       |
|  IMU_CTRL                          |SCHA634         |858    |597     |61      |725     |0       |0       |
|    CtrlData                        |CtrlData        |430    |375     |47      |333     |0       |0       |
|      usms                          |Time_1ms        |28     |23      |5       |20      |0       |0       |
|    SPIM                            |SPI_SCHA634     |428    |222     |14      |392     |0       |0       |
|  POWER                             |POWER_EN        |96     |50      |38      |36      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |599    |404     |103     |406     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |599    |404     |103     |406     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |256    |180     |0       |239     |0       |0       |
|        reg_inst                    |register        |253    |178     |0       |236     |0       |0       |
|        tap_inst                    |tap             |3      |2       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |343    |224     |103     |167     |0       |0       |
|        bus_inst                    |bus_top         |135    |86      |46      |54      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |27     |17      |10      |10      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |52     |32      |18      |20      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |51     |32      |18      |19      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |134    |101     |29      |85      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets   
    #1          1       13549  
    #2          2       3399   
    #3          3        699   
    #4          4        317   
    #5        5-10       953   
    #6        11-50      450   
    #7       51-100      10    
    #8       101-500      3    
    #9        >500        2    
  Average     2.14             

RUN-1002 : start command "export_db INS600M-21A_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_pr.db" in  2.180231s wall, 3.781250s user + 0.000000s system = 3.781250s CPU (173.4%)

RUN-1004 : used memory is 1058 MB, reserved memory is 1055 MB, peak memory is 1128 MB
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 67896, tnet num: 19444, tinst num: 8116, tnode num: 92022, tedge num: 111936.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.811976s wall, 1.812500s user + 0.000000s system = 1.812500s CPU (100.0%)

RUN-1004 : used memory is 1061 MB, reserved memory is 1057 MB, peak memory is 1128 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 19444 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in INS600M-21A_phy.timing, timing summary in INS600M-21A_phy.tsm.
RUN-1003 : finish command "report_timing -mode FINAL -net_info -rpt_autogen true -file INS600M-21A_phy.timing" in  1.519038s wall, 1.515625s user + 0.015625s system = 1.531250s CPU (100.8%)

RUN-1004 : used memory is 1069 MB, reserved memory is 1064 MB, peak memory is 1128 MB
RUN-1002 : start command "export_bid INS600M-21A_inst.bid"
PRG-1000 : <!-- HMAC is: 19285304e968e394d67a8f065444a1f66c49f77adeb419c41e6b7764d8228547 -->
RUN-1002 : start command "bitgen -bit INS600M-21A.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 12 threads.
BIT-1002 : Init instances completely, inst num: 8116
BIT-1002 : Init pips with 12 threads.
BIT-1002 : Init pips completely, net num: 19446, pip num: 147350
BIT-1002 : Init feedthrough with 12 threads.
BIT-1002 : Init feedthrough completely, num: 274
BIT-1003 : Multithreading accelaration with 12 threads.
BIT-1003 : Generate bitstream completely, there are 3236 valid insts, and 412705 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111001000110011001111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file INS600M-21A.bit.
RUN-1003 : finish command "bitgen -bit INS600M-21A.bit" in  12.752582s wall, 125.328125s user + 0.140625s system = 125.468750s CPU (983.9%)

RUN-1004 : used memory is 1186 MB, reserved memory is 1172 MB, peak memory is 1300 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250821_150902.log"

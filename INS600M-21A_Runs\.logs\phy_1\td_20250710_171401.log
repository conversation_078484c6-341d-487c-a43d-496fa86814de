============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = D:/softwawe/Anlogic/TD_5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     51593
   Run Date =   Thu Jul 10 17:14:01 2025

   Run on =     TLH-001
============================================================
RUN-1002 : start command "open_project INS600M-21A.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/Divider_gate.v
HDL-1007 : undeclared symbol 'open_n2', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(479)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(542)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(549)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(554)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(617)
HDL-1007 : undeclared symbol 'open_n9', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(624)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1135)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1198)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1205)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1652)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1715)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1722)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1735)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1798)
HDL-1007 : undeclared symbol 'open_n24', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(1805)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2308)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2371)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2378)
HDL-1007 : undeclared symbol 'open_n32', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2825)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2888)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(2895)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3342)
HDL-1007 : undeclared symbol 'open_n38', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3405)
HDL-1007 : undeclared symbol 'open_n39', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3412)
HDL-1007 : undeclared symbol 'open_n42', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3425)
HDL-1007 : undeclared symbol 'open_n43', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3488)
HDL-1007 : undeclared symbol 'open_n44', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3495)
HDL-1007 : undeclared symbol 'open_n47', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(3998)
HDL-1007 : undeclared symbol 'open_n48', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4061)
HDL-1007 : undeclared symbol 'open_n49', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4068)
HDL-1007 : undeclared symbol 'open_n52', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4922)
HDL-1007 : undeclared symbol 'open_n53', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4985)
HDL-1007 : undeclared symbol 'open_n54', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(4992)
HDL-1007 : undeclared symbol 'open_n57', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5005)
HDL-1007 : undeclared symbol 'open_n58', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5068)
HDL-1007 : undeclared symbol 'open_n59', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5075)
HDL-1007 : undeclared symbol 'open_n62', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5578)
HDL-1007 : undeclared symbol 'open_n63', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5641)
HDL-1007 : undeclared symbol 'open_n64', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(5648)
HDL-1007 : undeclared symbol 'open_n65', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6104)
HDL-1007 : undeclared symbol 'open_n68', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6109)
HDL-1007 : undeclared symbol 'open_n71', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6114)
HDL-1007 : undeclared symbol 'open_n74', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6182)
HDL-1007 : undeclared symbol 'open_n77', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6187)
HDL-1007 : undeclared symbol 'open_n80', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6601)
HDL-1007 : undeclared symbol 'open_n83', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6606)
HDL-1007 : undeclared symbol 'open_n86', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6639)
HDL-1007 : undeclared symbol 'open_n89', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(6644)
HDL-1007 : undeclared symbol 'open_n92', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7098)
HDL-1007 : undeclared symbol 'open_n95', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7145)
HDL-1007 : undeclared symbol 'open_n98', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7566)
HDL-1007 : undeclared symbol 'open_n101', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7620)
HDL-1007 : undeclared symbol 'open_n104', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7625)
HDL-1007 : undeclared symbol 'open_n105', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(7688)
HDL-1007 : undeclared symbol 'open_n108', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8198)
HDL-1007 : undeclared symbol 'open_n109', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8261)
HDL-1007 : undeclared symbol 'open_n110', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8268)
HDL-1007 : undeclared symbol 'open_n113', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8715)
HDL-1007 : undeclared symbol 'open_n114', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8778)
HDL-1007 : undeclared symbol 'open_n115', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(8785)
HDL-1007 : undeclared symbol 'open_n116', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9346)
HDL-1007 : undeclared symbol 'open_n119', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9351)
HDL-1007 : undeclared symbol 'open_n120', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9358)
HDL-1007 : undeclared symbol 'open_n121', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9407)
HDL-1007 : undeclared symbol 'open_n124', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9412)
HDL-1007 : undeclared symbol 'open_n125', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9419)
HDL-1007 : undeclared symbol 'open_n128', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9466)
HDL-1007 : undeclared symbol 'open_n129', assumed default net type 'wire' in ../../al_ip/Divider_gate.v(9634)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX115200E_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(271)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(276)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(297)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(302)
HDL-1007 : undeclared symbol 'open_n10', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(445)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(452)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(459)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(466)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(473)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(480)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(487)
HDL-1007 : undeclared symbol 'open_n17', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(494)
HDL-1007 : undeclared symbol 'open_n18', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(671)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(676)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(704)
HDL-1007 : undeclared symbol 'open_n25', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(709)
HDL-1007 : undeclared symbol 'open_n26', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(998)
HDL-1007 : undeclared symbol 'open_n27', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1005)
HDL-1007 : undeclared symbol 'open_n28', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1012)
HDL-1007 : undeclared symbol 'open_n29', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1019)
HDL-1007 : undeclared symbol 'open_n30', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1026)
HDL-1007 : undeclared symbol 'open_n33', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1031)
HDL-1007 : undeclared symbol 'open_n34', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1038)
HDL-1007 : undeclared symbol 'open_n35', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1045)
HDL-1007 : undeclared symbol 'open_n36', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1052)
HDL-1007 : undeclared symbol 'open_n37', assumed default net type 'wire' in ../../al_ip/UART_RX115200E_gate.v(1059)
HDL-1007 : analyze verilog file ../../al_ip/UART_RX460800_gate.v
HDL-1007 : undeclared symbol 'open_n0', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(205)
HDL-1007 : undeclared symbol 'open_n3', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(210)
HDL-1007 : undeclared symbol 'open_n4', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(460)
HDL-1007 : undeclared symbol 'open_n7', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(465)
HDL-1007 : undeclared symbol 'open_n8', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(493)
HDL-1007 : undeclared symbol 'open_n11', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(498)
HDL-1007 : undeclared symbol 'open_n12', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(809)
HDL-1007 : undeclared symbol 'open_n13', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(816)
HDL-1007 : undeclared symbol 'open_n14', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(823)
HDL-1007 : undeclared symbol 'open_n15', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(830)
HDL-1007 : undeclared symbol 'open_n16', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(837)
HDL-1007 : undeclared symbol 'open_n19', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(842)
HDL-1007 : undeclared symbol 'open_n20', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(849)
HDL-1007 : undeclared symbol 'open_n21', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(856)
HDL-1007 : undeclared symbol 'open_n22', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(863)
HDL-1007 : undeclared symbol 'open_n23', assumed default net type 'wire' in ../../al_ip/UART_RX460800_gate.v(870)
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo8x8.v
HDL-1007 : analyze verilog file ../../Src/FMC/Data_Processing.v
HDL-1007 : analyze verilog file ../../Src/FMC/FMC_Ctrl.v
HDL-1007 : analyze verilog file ../../Src/FMC/genclk.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Agrica.v
HDL-1007 : analyze verilog file ../../Src/GNSS/Gprmc.v
HDL-1007 : analyze verilog file ../../Src/GNSS/PPPNAV.v
HDL-1007 : analyze verilog file ../../Src/GNSS/STADOP.v
HDL-1007 : analyze verilog file ../../Src/GNSS/uniheading.v
HDL-1007 : analyze verilog file ../../Src/IFOG/AnyFog.v
HDL-1007 : analyze verilog file ../../Src/IIC/I2C_master.v
HDL-5007 WARNING: literal value 'b11 truncated to fit in 1 bits in ../../Src/IIC/I2C_master.v(472)
HDL-1007 : analyze verilog file ../../Src/INS600M-21A.v
HDL-1007 : undeclared symbol 'reset', assumed default net type 'wire' in ../../Src/INS600M-21A.v(101)
HDL-7007 CRITICAL-WARNING: 'reset' is already implicitly declared on line 101 in ../../Src/INS600M-21A.v(104)
HDL-1007 : undeclared symbol 'DACC_valid', assumed default net type 'wire' in ../../Src/INS600M-21A.v(520)
HDL-1007 : analyze verilog file ../../Src/POWER/POWER_EN.v
HDL-1007 : analyze verilog file ../../Src/SPI/CtrlData.v
HDL-1007 : analyze verilog file ../../Src/SPI/SCHA634.v
HDL-1007 : analyze verilog file ../../Src/SPI/SPI_MASTER.v
HDL-5007 WARNING: parameter 'IDLE' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(55)
HDL-5007 WARNING: parameter 'WAIT1' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(56)
HDL-5007 WARNING: parameter 'DATA' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(57)
HDL-5007 WARNING: parameter 'WAIT2' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(58)
HDL-5007 WARNING: parameter 'FINISH' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(59)
HDL-5007 WARNING: parameter 'CNT_MAX' becomes localparam in 'SPI_SCHA634' with formal parameter declaration list in ../../Src/SPI/SPI_MASTER.v(60)
HDL-1007 : analyze verilog file ../../Src/SPI/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src/UART/COM2_Control.v
HDL-1007 : analyze verilog file ../../Src/UART/COM3_Control.v
HDL-1007 : undeclared symbol 'GPRMC_sta', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(57)
HDL-1007 : undeclared symbol 'GPRMC_end', assumed default net type 'wire' in ../../Src/UART/COM3_Control.v(58)
HDL-1007 : analyze verilog file ../../Src/GNSS/GNRMC_Tx.v
HDL-5007 WARNING: data object 'header_index' is already declared in ../../Src/GNSS/GNRMC_Tx.v(39)
HDL-1007 : previous declaration of 'header_index' is from here in ../../Src/GNSS/GNRMC_Tx.v(28)
HDL-5007 WARNING: second declaration of 'header_index' is ignored in ../../Src/GNSS/GNRMC_Tx.v(39)
HDL-5007 WARNING: data object 'header_sending' is already declared in ../../Src/GNSS/GNRMC_Tx.v(40)
HDL-1007 : previous declaration of 'header_sending' is from here in ../../Src/GNSS/GNRMC_Tx.v(27)
HDL-5007 WARNING: second declaration of 'header_sending' is ignored in ../../Src/GNSS/GNRMC_Tx.v(40)
RUN-1001 : Project manager successfully analyzed 24 source files.
RUN-1003 : finish command "open_project INS600M-21A.prj" in  5.233835s wall, 1.328125s user + 3.843750s system = 5.171875s CPU (98.8%)

RUN-1004 : used memory is 79 MB, reserved memory is 41 MB, peak memory is 91 MB
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/INS600M-21A_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db ../syn_1/INS600M-21A_gate.db" in  1.956227s wall, 1.875000s user + 0.062500s system = 1.937500s CPU (99.0%)

RUN-1004 : used memory is 324 MB, reserved memory is 294 MB, peak memory is 328 MB
RUN-1002 : start command "read_sdc ../../Constraints/INS600M_21A.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 40 -waveform 0 20 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 40000, rise: 0, fall: 20000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK100M/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.0000 [get_pins {CLK100M/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK100M/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK100M/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/FMC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 10 view nodes, 47 trigger nets, 47 data nets.
KIT-1004 : Chipwatcher code = 1100100110110000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir D:/softwawe/Anlogic/TD_5.6.2/cw/ -file INS600M-21A_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\register.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v
HDL-1007 : analyze verilog file D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file INS600M-21A_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in INS600M-21A_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in D:/softwawe/Anlogic/TD_5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000}) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010000) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb010) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in D:/softwawe/Anlogic/TD_5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in D:/softwawe/Anlogic/TD_5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in D:/softwawe/Anlogic/TD_5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "INS600M_21A"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=10,BUS_DIN_NUM=47,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01000,32'sb010000,32'sb010,32'sb01,32'sb01,32'sb01,32'sb01000,32'sb01000,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01000,32'sb011000,32'sb011010,32'sb011011,32'sb011100,32'sb011101,32'sb0100101,32'sb0101101,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb010100,32'sb0111000,32'sb01000000,32'sb01000110,32'sb01001100,32'sb01010010,32'sb01100110,32'sb01111010,32'sb010000000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb010)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model INS600M_21A
SYN-1032 : 26345/42 useful/useless nets, 23030/25 useful/useless insts
SYN-1016 : Merged 49 instances.
SYN-1032 : 25933/26 useful/useless nets, 23501/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 5 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 5 mux instances.
SYN-1015 : Optimize round 1, 472 better
SYN-1014 : Optimize round 2
SYN-1032 : 25522/75 useful/useless nets, 23090/80 useful/useless insts
SYN-1015 : Optimize round 2, 160 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl -no_sch" in  2.455901s wall, 2.406250s user + 0.046875s system = 2.453125s CPU (99.9%)

RUN-1004 : used memory is 351 MB, reserved memory is 320 MB, peak memory is 353 MB
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 66 IOs to PADs
RUN-1002 : start command "update_pll_param -module INS600M_21A"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 25582/367 useful/useless nets, 23191/52 useful/useless insts
SYN-1016 : Merged 57 instances.
SYN-2571 : Optimize after map_dsp, round 1, 476 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 43 instances.
SYN-2501 : Optimize round 1, 87 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 20 macro adder
SYN-1019 : Optimized 21 mux instances.
SYN-1016 : Merged 17 instances.
SYN-1032 : 26059/5 useful/useless nets, 23668/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 98241, tnet num: 26059, tinst num: 23667, tnode num: 137269, tedge num: 153119.
TMR-2508 : Levelizing timing graph completed, there are 53 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -prepack" in  1.308724s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (100.3%)

RUN-1004 : used memory is 518 MB, reserved memory is 488 MB, peak memory is 518 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 26059 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 265 (3.48), #lev = 7 (1.86)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 656 instances into 265 LUTs, name keeping = 71%.
SYN-1001 : Packing model "INS600M_21A" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 477 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 132 adder to BLE ...
SYN-4008 : Packed 132 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  4.742393s wall, 4.625000s user + 0.140625s system = 4.765625s CPU (100.5%)

RUN-1004 : used memory is 387 MB, reserved memory is 375 MB, peak memory is 647 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  7.513496s wall, 7.343750s user + 0.187500s system = 7.531250s CPU (100.2%)

RUN-1004 : used memory is 388 MB, reserved memory is 375 MB, peak memory is 647 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model INS600M_21A
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[7] will be merged to another kept net COM2/uart_com2/AGRIC_data[7]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[6] will be merged to another kept net COM2/uart_com2/AGRIC_data[6]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[5] will be merged to another kept net COM2/uart_com2/AGRIC_data[5]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[4] will be merged to another kept net COM2/uart_com2/AGRIC_data[4]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[3] will be merged to another kept net COM2/uart_com2/AGRIC_data[3]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[2] will be merged to another kept net COM2/uart_com2/AGRIC_data[2]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[1] will be merged to another kept net COM2/uart_com2/AGRIC_data[1]
SYN-5055 WARNING: The kept net COM2/PPPNAV_com2/PPPNAV_data[0] will be merged to another kept net COM2/uart_com2/AGRIC_data[0]
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_end will be merged to another kept net COM3/GNRMC/GPRMC_end
SYN-5055 WARNING: The kept net COM3/rmc_com3/GPRMC_sta will be merged to another kept net COM3/GNRMC/GPRMC_sta
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (328 clock/control pins, 0 other pins).
SYN-4027 : Net COM3/GNRMC/u_fifo/ram_inst/clkb is clkc0 of pll CLK100M/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK100M/pll_inst.
SYN-4025 : Tag rtl::Net COM3/GNRMC/u_fifo/ram_inst/clkb as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 22893 instances
RUN-0007 : 7112 luts, 14216 seqs, 956 mslices, 502 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 25308 nets
RUN-1001 : 19585 nets have 2 pins
RUN-1001 : 4224 nets have [3 - 5] pins
RUN-1001 : 1099 nets have [6 - 10] pins
RUN-1001 : 255 nets have [11 - 20] pins
RUN-1001 : 110 nets have [21 - 99] pins
RUN-1001 : 35 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |    4759     
RUN-1001 :   No   |  No   |  Yes  |     679     
RUN-1001 :   No   |  Yes  |  No   |     88      
RUN-1001 :   Yes  |  No   |  No   |    8131     
RUN-1001 :   Yes  |  No   |  Yes  |     508     
RUN-1001 :   Yes  |  Yes  |  No   |     51      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |  370  |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 379
PHY-3001 : Initial placement ...
PHY-3001 : design contains 22891 instances, 7112 luts, 14216 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Huge net DATA/AGRIC_AltStddy_b_n with 1163 pins
PHY-3001 : Huge net DATA/done_div with 1682 pins
PHY-0007 : Cell area utilization is 73%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96537, tnet num: 25306, tinst num: 22891, tnode num: 135582, tedge num: 151583.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.254533s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (99.6%)

RUN-1004 : used memory is 587 MB, reserved memory is 562 MB, peak memory is 647 MB
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 25306 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.190855s wall, 2.125000s user + 0.062500s system = 2.187500s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 4.87041e+06
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 22891.
PHY-3001 : Level 1 #clusters 3278.
PHY-3001 : End clustering;  0.171439s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (145.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 73%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 966496, overlap = 724.969
PHY-3002 : Step(2): len = 863423, overlap = 808.281
PHY-3002 : Step(3): len = 599435, overlap = 1006
PHY-3002 : Step(4): len = 525491, overlap = 1084.69
PHY-3002 : Step(5): len = 422599, overlap = 1219.5
PHY-3002 : Step(6): len = 373901, overlap = 1301.69
PHY-3002 : Step(7): len = 308916, overlap = 1410.47
PHY-3002 : Step(8): len = 273384, overlap = 1488.56
PHY-3002 : Step(9): len = 239134, overlap = 1572.31
PHY-3002 : Step(10): len = 219231, overlap = 1633.88
PHY-3002 : Step(11): len = 192612, overlap = 1695.75
PHY-3002 : Step(12): len = 176706, overlap = 1718.91
PHY-3002 : Step(13): len = 159263, overlap = 1737.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.0067e-06
PHY-3002 : Step(14): len = 163459, overlap = 1732.19
PHY-3002 : Step(15): len = 213948, overlap = 1593.41
PHY-3002 : Step(16): len = 210574, overlap = 1480.81
PHY-3002 : Step(17): len = 225163, overlap = 1406.91
PHY-3002 : Step(18): len = 209928, overlap = 1387.69
PHY-3002 : Step(19): len = 213254, overlap = 1375.06
PHY-3002 : Step(20): len = 201977, overlap = 1356.09
PHY-3002 : Step(21): len = 202596, overlap = 1341.62
PHY-3002 : Step(22): len = 194613, overlap = 1346.56
PHY-3002 : Step(23): len = 195213, overlap = 1352.16
PHY-3002 : Step(24): len = 187890, overlap = 1347.66
PHY-3002 : Step(25): len = 188635, overlap = 1330.78
PHY-3002 : Step(26): len = 183134, overlap = 1343.84
PHY-3002 : Step(27): len = 182948, overlap = 1329.69
PHY-3002 : Step(28): len = 179651, overlap = 1307.56
PHY-3002 : Step(29): len = 178896, overlap = 1291.34
PHY-3002 : Step(30): len = 176214, overlap = 1289.41
PHY-3002 : Step(31): len = 176250, overlap = 1281.31
PHY-3002 : Step(32): len = 172926, overlap = 1277.81
PHY-3002 : Step(33): len = 172740, overlap = 1269.72
PHY-3002 : Step(34): len = 170304, overlap = 1276.34
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.0134e-06
PHY-3002 : Step(35): len = 179644, overlap = 1226.72
PHY-3002 : Step(36): len = 193322, overlap = 1147.09
PHY-3002 : Step(37): len = 195513, overlap = 1124.78
PHY-3002 : Step(38): len = 198237, overlap = 1086.38
PHY-3002 : Step(39): len = 197692, overlap = 1060
PHY-3002 : Step(40): len = 197886, overlap = 1048.22
PHY-3002 : Step(41): len = 196796, overlap = 1043.91
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.0268e-06
PHY-3002 : Step(42): len = 207453, overlap = 1020.34
PHY-3002 : Step(43): len = 223739, overlap = 998.375
PHY-3002 : Step(44): len = 228637, overlap = 972.812
PHY-3002 : Step(45): len = 233227, overlap = 964.75
PHY-3002 : Step(46): len = 233235, overlap = 962.125
PHY-3002 : Step(47): len = 233298, overlap = 951.938
PHY-3002 : Step(48): len = 232498, overlap = 931.219
PHY-3002 : Step(49): len = 233581, overlap = 916.844
PHY-3002 : Step(50): len = 233856, overlap = 898.281
PHY-3002 : Step(51): len = 235243, overlap = 902.094
PHY-3002 : Step(52): len = 234687, overlap = 910.562
PHY-3002 : Step(53): len = 234671, overlap = 917.781
PHY-3002 : Step(54): len = 232631, overlap = 925.531
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 8.0536e-06
PHY-3002 : Step(55): len = 246038, overlap = 888.625
PHY-3002 : Step(56): len = 262207, overlap = 808.156
PHY-3002 : Step(57): len = 266013, overlap = 789.844
PHY-3002 : Step(58): len = 267857, overlap = 765.312
PHY-3002 : Step(59): len = 266975, overlap = 760.719
PHY-3002 : Step(60): len = 265589, overlap = 777.281
PHY-3002 : Step(61): len = 263987, overlap = 774.125
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 1.61072e-05
PHY-3002 : Step(62): len = 277000, overlap = 737.812
PHY-3002 : Step(63): len = 295134, overlap = 666.438
PHY-3002 : Step(64): len = 302241, overlap = 595.312
PHY-3002 : Step(65): len = 305329, overlap = 567.594
PHY-3002 : Step(66): len = 303580, overlap = 557.812
PHY-3002 : Step(67): len = 302512, overlap = 546.781
PHY-3002 : Step(68): len = 299842, overlap = 543.781
PHY-3002 : Step(69): len = 299905, overlap = 537
PHY-3002 : Step(70): len = 298673, overlap = 535.344
PHY-3002 : Step(71): len = 298525, overlap = 540.75
PHY-3002 : Step(72): len = 297470, overlap = 519.281
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 3.22144e-05
PHY-3002 : Step(73): len = 308555, overlap = 502.656
PHY-3002 : Step(74): len = 316954, overlap = 485.781
PHY-3002 : Step(75): len = 320575, overlap = 464.438
PHY-3002 : Step(76): len = 321462, overlap = 449.594
PHY-3002 : Step(77): len = 318708, overlap = 432.688
PHY-3002 : Step(78): len = 318199, overlap = 423.031
PHY-3002 : Step(79): len = 316360, overlap = 409.75
PHY-3002 : Step(80): len = 317434, overlap = 408.562
PHY-3002 : Step(81): len = 316964, overlap = 412.156
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 6.44288e-05
PHY-3002 : Step(82): len = 325272, overlap = 394.688
PHY-3002 : Step(83): len = 331664, overlap = 382.812
PHY-3002 : Step(84): len = 334547, overlap = 374.219
PHY-3002 : Step(85): len = 336108, overlap = 369.406
PHY-3002 : Step(86): len = 335227, overlap = 361.719
PHY-3002 : Step(87): len = 334937, overlap = 365.625
PHY-3002 : Step(88): len = 333868, overlap = 366.656
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000120504
PHY-3002 : Step(89): len = 338278, overlap = 370.875
PHY-3002 : Step(90): len = 342779, overlap = 354.406
PHY-3002 : Step(91): len = 345396, overlap = 347.406
PHY-3002 : Step(92): len = 346474, overlap = 353.125
PHY-3002 : Step(93): len = 346928, overlap = 348.562
PHY-3002 : Step(94): len = 347226, overlap = 343.656
PHY-3002 : Step(95): len = 346924, overlap = 340.844
PHY-3002 : Step(96): len = 347420, overlap = 325.344
PHY-3002 : Step(97): len = 347027, overlap = 329.094
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000234401
PHY-3002 : Step(98): len = 348848, overlap = 320.906
PHY-3002 : Step(99): len = 352960, overlap = 312.531
PHY-3002 : Step(100): len = 355180, overlap = 302.531
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.000454264
PHY-3002 : Step(101): len = 356278, overlap = 287
PHY-3002 : Step(102): len = 361888, overlap = 262.094
PHY-3002 : Step(103): len = 364851, overlap = 255.875
PHY-3002 : Step(104): len = 367077, overlap = 260.938
PHY-3002 : Step(105): len = 367501, overlap = 274.938
PHY-3002 : Step(106): len = 367058, overlap = 272.062
PHY-3002 : Step(107): len = 366084, overlap = 267.531
PHY-3002 : Step(108): len = 365882, overlap = 275.406
PHY-3002 : Step(109): len = 365486, overlap = 273.844
PHY-3002 : Step(110): len = 365442, overlap = 271.469
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011988s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (130.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/25308.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 483264, over cnt = 1564(4%), over = 7468, worst = 30
PHY-1001 : End global iterations;  0.866296s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (140.7%)

PHY-1001 : Congestion index: top1 = 72.16, top5 = 56.88, top10 = 48.47, top15 = 43.25.
PHY-3001 : End congestion estimation;  1.092954s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (133.0%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25306 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.040301s wall, 1.000000s user + 0.015625s system = 1.015625s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.6635e-05
PHY-3002 : Step(111): len = 411511, overlap = 215.656
PHY-3002 : Step(112): len = 433204, overlap = 230.719
PHY-3002 : Step(113): len = 431369, overlap = 239.062
PHY-3002 : Step(114): len = 430535, overlap = 247.219
PHY-3002 : Step(115): len = 436365, overlap = 240.531
PHY-3002 : Step(116): len = 435693, overlap = 242.125
PHY-3002 : Step(117): len = 434531, overlap = 241.062
PHY-3002 : Step(118): len = 437643, overlap = 246.688
PHY-3002 : Step(119): len = 435684, overlap = 250.531
PHY-3002 : Step(120): len = 435556, overlap = 250.062
PHY-3002 : Step(121): len = 435698, overlap = 255.062
PHY-3002 : Step(122): len = 435550, overlap = 246.219
PHY-3002 : Step(123): len = 436737, overlap = 238.094
PHY-3002 : Step(124): len = 435406, overlap = 234.188
PHY-3002 : Step(125): len = 435994, overlap = 225.312
PHY-3002 : Step(126): len = 434084, overlap = 223.625
PHY-3002 : Step(127): len = 432572, overlap = 223.688
PHY-3002 : Step(128): len = 432550, overlap = 223.844
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00017327
PHY-3002 : Step(129): len = 432322, overlap = 224.375
PHY-3002 : Step(130): len = 433602, overlap = 223.844
PHY-3002 : Step(131): len = 436132, overlap = 224.781
PHY-3002 : Step(132): len = 440577, overlap = 224.562
PHY-3002 : Step(133): len = 443403, overlap = 229.656
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00034654
PHY-3002 : Step(134): len = 446180, overlap = 224.406
PHY-3002 : Step(135): len = 449334, overlap = 221.031
PHY-3002 : Step(136): len = 456155, overlap = 229.406
PHY-3002 : Step(137): len = 464999, overlap = 228.344
PHY-3002 : Step(138): len = 466176, overlap = 225.281
PHY-3002 : Step(139): len = 465982, overlap = 220.094
PHY-3002 : Step(140): len = 466717, overlap = 216.781
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00069308
PHY-3002 : Step(141): len = 466932, overlap = 217.531
PHY-3002 : Step(142): len = 473612, overlap = 214.281
PHY-3002 : Step(143): len = 484051, overlap = 211.531
PHY-3002 : Step(144): len = 485554, overlap = 203.031
PHY-3002 : Step(145): len = 484358, overlap = 195.438
PHY-3002 : Step(146): len = 484168, overlap = 188.344
PHY-3002 : Step(147): len = 483311, overlap = 179.969
PHY-3002 : Step(148): len = 482709, overlap = 173.312
PHY-3002 : Step(149): len = 480359, overlap = 170.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(150): len = 481642, overlap = 171.188
PHY-3002 : Step(151): len = 485859, overlap = 173.281
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 78%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 123/25308.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 555952, over cnt = 2471(7%), over = 14002, worst = 110
PHY-1001 : End global iterations;  0.966151s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (198.9%)

PHY-1001 : Congestion index: top1 = 102.20, top5 = 72.95, top10 = 60.49, top15 = 53.71.
PHY-3001 : End congestion estimation;  1.227872s wall, 2.171875s user + 0.015625s system = 2.187500s CPU (178.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25306 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.037600s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.77376e-05
PHY-3002 : Step(152): len = 486781, overlap = 552.375
PHY-3002 : Step(153): len = 494081, overlap = 459.125
PHY-3002 : Step(154): len = 479111, overlap = 437.281
PHY-3002 : Step(155): len = 473353, overlap = 411.844
PHY-3002 : Step(156): len = 466362, overlap = 377.031
PHY-3002 : Step(157): len = 462516, overlap = 362.344
PHY-3002 : Step(158): len = 456927, overlap = 357.125
PHY-3002 : Step(159): len = 452807, overlap = 359.188
PHY-3002 : Step(160): len = 448981, overlap = 357.906
PHY-3002 : Step(161): len = 446269, overlap = 344.031
PHY-3002 : Step(162): len = 445754, overlap = 332.562
PHY-3002 : Step(163): len = 440814, overlap = 332.031
PHY-3002 : Step(164): len = 439453, overlap = 326.906
PHY-3002 : Step(165): len = 438699, overlap = 320.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000195475
PHY-3002 : Step(166): len = 438858, overlap = 310.281
PHY-3002 : Step(167): len = 441838, overlap = 304.031
PHY-3002 : Step(168): len = 444080, overlap = 302.031
PHY-3002 : Step(169): len = 444331, overlap = 297.812
PHY-3002 : Step(170): len = 444732, overlap = 293.594
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000390951
PHY-3002 : Step(171): len = 446466, overlap = 289.75
PHY-3002 : Step(172): len = 451273, overlap = 276.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000718447
PHY-3002 : Step(173): len = 455421, overlap = 259.188
PHY-3002 : Step(174): len = 462042, overlap = 246.344
PHY-3002 : Step(175): len = 466134, overlap = 232.781
PHY-3002 : Step(176): len = 466238, overlap = 226.656
PHY-3002 : Step(177): len = 468043, overlap = 216.938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00127909
PHY-3002 : Step(178): len = 468890, overlap = 217.094
PHY-3002 : Step(179): len = 469661, overlap = 212.219
PHY-3002 : Step(180): len = 473078, overlap = 207.812
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00206957
PHY-3002 : Step(181): len = 473992, overlap = 207.938
PHY-3002 : Step(182): len = 477570, overlap = 204.406
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 96537, tnet num: 25306, tinst num: 22891, tnode num: 135582, tedge num: 151583.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.653690s wall, 1.593750s user + 0.062500s system = 1.656250s CPU (100.2%)

RUN-1004 : used memory is 627 MB, reserved memory is 605 MB, peak memory is 786 MB
OPT-1001 : Total overflow 640.09 peak overflow 4.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 537/25308.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 573832, over cnt = 2908(8%), over = 11767, worst = 32
PHY-1001 : End global iterations;  1.217145s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (160.5%)

PHY-1001 : Congestion index: top1 = 63.15, top5 = 53.17, top10 = 47.91, top15 = 44.49.
PHY-1001 : End incremental global routing;  1.463126s wall, 2.171875s user + 0.031250s system = 2.203125s CPU (150.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25306 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.101486s wall, 1.078125s user + 0.015625s system = 1.093750s CPU (99.3%)

OPT-1001 : 21 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 22807 has valid locations, 294 needs to be replaced
PHY-3001 : design contains 23164 instances, 7262 luts, 14339 seqs, 1458 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 498759
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 19297/25581.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 591840, over cnt = 2977(8%), over = 11809, worst = 32
PHY-1001 : End global iterations;  0.222850s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (168.3%)

PHY-1001 : Congestion index: top1 = 63.90, top5 = 53.78, top10 = 48.40, top15 = 44.95.
PHY-3001 : End congestion estimation;  0.479285s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (130.4%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 97499, tnet num: 25579, tinst num: 23164, tnode num: 136840, tedge num: 152961.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.621006s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (100.2%)

RUN-1004 : used memory is 683 MB, reserved memory is 678 MB, peak memory is 787 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25579 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  3.064927s wall, 3.000000s user + 0.062500s system = 3.062500s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(183): len = 498218, overlap = 9.4375
PHY-3002 : Step(184): len = 499329, overlap = 9.25
PHY-3002 : Step(185): len = 500328, overlap = 9.375
PHY-3002 : Step(186): len = 500667, overlap = 9.4375
PHY-3002 : Step(187): len = 501766, overlap = 9.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.56216e-05
PHY-3002 : Step(188): len = 501767, overlap = 9.8125
PHY-3002 : Step(189): len = 502076, overlap = 9.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(190): len = 502212, overlap = 9.3125
PHY-3002 : Step(191): len = 502451, overlap = 9.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 79%
PHY-3001 : Analyzing congestion ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Incremental mode ON
PHY-1001 : Reuse net number 19318/25581.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 590448, over cnt = 2966(8%), over = 11890, worst = 32
PHY-1001 : End global iterations;  0.214978s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (167.2%)

PHY-1001 : Congestion index: top1 = 65.24, top5 = 54.35, top10 = 48.84, top15 = 45.26.
PHY-3001 : End congestion estimation;  0.479814s wall, 0.593750s user + 0.031250s system = 0.625000s CPU (130.3%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 25579 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.074605s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000343659
PHY-3002 : Step(192): len = 502274, overlap = 208.375
PHY-3002 : Step(193): len = 502396, overlap = 208.781
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000687318
PHY-3002 : Step(194): len = 502569, overlap = 208.125
PHY-3002 : Step(195): len = 502865, overlap = 207.781
PHY-3001 : Final: Len = 502865, Over = 207.781
PHY-3001 : End incremental placement;  6.364487s wall, 6.859375s user + 0.421875s system = 7.281250s CPU (114.4%)

OPT-1001 : Total overflow 646.84 peak overflow 4.56
OPT-1001 : End high-fanout net optimization;  9.543928s wall, 10.875000s user + 0.468750s system = 11.343750s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 790, reserve = 773, peak = 809.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 19372/25581.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 592752, over cnt = 2932(8%), over = 11147, worst = 32
PHY-1002 : len = 644312, over cnt = 2460(6%), over = 7195, worst = 23
PHY-1002 : len = 705520, over cnt = 1276(3%), over = 3362, worst = 20
PHY-1002 : len = 749528, over cnt = 536(1%), over = 1174, worst = 13
PHY-1002 : len = 772376, over cnt = 13(0%), over = 20, worst = 4
PHY-1001 : End global iterations;  1.777378s wall, 2.421875s user + 0.015625s system = 2.437500s CPU (137.1%)

PHY-1001 : Congestion index: top1 = 55.60, top5 = 48.79, top10 = 45.56, top15 = 43.47.
OPT-1001 : End congestion update;  2.035045s wall, 2.671875s user + 0.015625s system = 2.687500s CPU (132.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 25579 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.948692s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (100.5%)

OPT-0007 : Start: WNS 3190 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  2.990994s wall, 3.640625s user + 0.015625s system = 3.656250s CPU (122.2%)

OPT-1001 : Current memory(MB): used = 764, reserve = 752, peak = 809.
OPT-1001 : End physical optimization;  14.549859s wall, 16.640625s user + 0.546875s system = 17.187500s CPU (118.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 7262 LUT to BLE ...
SYN-4008 : Packed 7262 LUT and 2711 SEQ to BLE.
SYN-4003 : Packing 11628 remaining SEQ's ...
SYN-4005 : Packed 4991 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 6637 single SEQ's are left
SYN-4011 : Packing model "INS600M_21A" (AL_USER_NORMAL) with 13899/15638 primitive instances ...
PHY-3001 : End packing;  3.489114s wall, 3.484375s user + 0.000000s system = 3.484375s CPU (99.9%)

PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 9157 instances
RUN-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22978 nets
RUN-1001 : 16856 nets have 2 pins
RUN-1001 : 4508 nets have [3 - 5] pins
RUN-1001 : 1022 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 217 nets have [21 - 99] pins
RUN-1001 : 14 nets have 100+ pins
PHY-3001 : design contains 9155 instances, 9050 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Cell area utilization is 94%
PHY-3001 : After packing: Len = 537875, Over = 478
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 8810/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 718728, over cnt = 2304(6%), over = 4052, worst = 10
PHY-1002 : len = 730496, over cnt = 1568(4%), over = 2216, worst = 8
PHY-1002 : len = 746768, over cnt = 756(2%), over = 1028, worst = 8
PHY-1002 : len = 760440, over cnt = 253(0%), over = 329, worst = 6
PHY-1002 : len = 767888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  1.464511s wall, 2.531250s user + 0.015625s system = 2.546875s CPU (173.9%)

PHY-1001 : Congestion index: top1 = 56.38, top5 = 49.79, top10 = 46.07, top15 = 43.64.
PHY-3001 : End congestion estimation;  1.851866s wall, 2.921875s user + 0.015625s system = 2.937500s CPU (158.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81765, tnet num: 22976, tinst num: 9155, tnode num: 110174, tedge num: 133671.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.871552s wall, 1.843750s user + 0.015625s system = 1.859375s CPU (99.3%)

RUN-1004 : used memory is 665 MB, reserved memory is 653 MB, peak memory is 809 MB
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  2.871601s wall, 2.828125s user + 0.031250s system = 2.859375s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.81404e-05
PHY-3002 : Step(196): len = 531758, overlap = 455
PHY-3002 : Step(197): len = 527965, overlap = 469.75
PHY-3002 : Step(198): len = 520329, overlap = 471.25
PHY-3002 : Step(199): len = 515646, overlap = 474.5
PHY-3002 : Step(200): len = 511346, overlap = 487.5
PHY-3002 : Step(201): len = 508950, overlap = 489.5
PHY-3002 : Step(202): len = 505072, overlap = 486.25
PHY-3002 : Step(203): len = 502964, overlap = 497.5
PHY-3002 : Step(204): len = 501248, overlap = 504.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.62809e-05
PHY-3002 : Step(205): len = 504719, overlap = 494.5
PHY-3002 : Step(206): len = 509522, overlap = 483.75
PHY-3002 : Step(207): len = 508678, overlap = 479.25
PHY-3002 : Step(208): len = 509002, overlap = 477.25
PHY-3002 : Step(209): len = 510126, overlap = 474.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(210): len = 517341, overlap = 461.25
PHY-3002 : Step(211): len = 523089, overlap = 448.25
PHY-3002 : Step(212): len = 523055, overlap = 447.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.526574s wall, 0.875000s user + 0.375000s system = 1.250000s CPU (237.4%)

PHY-3001 : Trial Legalized: Len = 837798
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 93%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 740/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 962832, over cnt = 3390(9%), over = 5827, worst = 7
PHY-1002 : len = 989272, over cnt = 2064(5%), over = 2902, worst = 7
PHY-1002 : len = 1.02118e+06, over cnt = 582(1%), over = 790, worst = 6
PHY-1002 : len = 1.03486e+06, over cnt = 122(0%), over = 125, worst = 2
PHY-1002 : len = 1.03885e+06, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.620554s wall, 4.281250s user + 0.062500s system = 4.343750s CPU (165.8%)

PHY-1001 : Congestion index: top1 = 58.99, top5 = 53.41, top10 = 50.43, top15 = 48.41.
PHY-3001 : End congestion estimation;  3.031106s wall, 4.687500s user + 0.062500s system = 4.750000s CPU (156.7%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.011018s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000491946
PHY-3002 : Step(213): len = 748334, overlap = 154
PHY-3002 : Step(214): len = 701789, overlap = 177.5
PHY-3002 : Step(215): len = 675720, overlap = 198.25
PHY-3002 : Step(216): len = 661012, overlap = 213.5
PHY-3002 : Step(217): len = 647782, overlap = 243.75
PHY-3002 : Step(218): len = 635617, overlap = 262
PHY-3002 : Step(219): len = 626659, overlap = 274.75
PHY-3002 : Step(220): len = 620389, overlap = 286.25
PHY-3002 : Step(221): len = 614772, overlap = 303.75
PHY-3002 : Step(222): len = 610568, overlap = 313
PHY-3002 : Step(223): len = 607273, overlap = 320
PHY-3002 : Step(224): len = 604073, overlap = 327
PHY-3002 : Step(225): len = 601595, overlap = 327
PHY-3002 : Step(226): len = 599602, overlap = 329.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000960193
PHY-3002 : Step(227): len = 603589, overlap = 325.5
PHY-3002 : Step(228): len = 609534, overlap = 326.5
PHY-3002 : Step(229): len = 612838, overlap = 326
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00170279
PHY-3002 : Step(230): len = 615092, overlap = 324.5
PHY-3002 : Step(231): len = 622771, overlap = 321.25
PHY-3002 : Step(232): len = 628590, overlap = 318.25
PHY-3002 : Step(233): len = 629561, overlap = 318.25
PHY-3002 : Step(234): len = 632672, overlap = 324.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.176739s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.1%)

PHY-3001 : Legalized: Len = 708328, Over = 0
PHY-3001 : Spreading special nets. 56 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.090676s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.4%)

PHY-3001 : 96 instances has been re-located, deltaX = 48, deltaY = 60, maxDist = 4.
PHY-3001 : Final: Len = 710188, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81765, tnet num: 22976, tinst num: 9155, tnode num: 110174, tedge num: 133671.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.205928s wall, 2.203125s user + 0.000000s system = 2.203125s CPU (99.9%)

RUN-1004 : used memory is 668 MB, reserved memory is 667 MB, peak memory is 809 MB
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 3075/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 836040, over cnt = 3232(9%), over = 5410, worst = 6
PHY-1002 : len = 856136, over cnt = 2155(6%), over = 3025, worst = 5
PHY-1002 : len = 886472, over cnt = 742(2%), over = 954, worst = 5
PHY-1002 : len = 900616, over cnt = 228(0%), over = 252, worst = 2
PHY-1002 : len = 908520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.482091s wall, 3.843750s user + 0.062500s system = 3.906250s CPU (157.4%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 51.48, top10 = 48.71, top15 = 46.83.
PHY-1001 : End incremental global routing;  2.818208s wall, 4.187500s user + 0.062500s system = 4.250000s CPU (150.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  1.006506s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (99.4%)

OPT-1001 : 0 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model INS600M_21A.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 9092 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 9155 instances, 9050 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : End incremental placement; No cells to be placed.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  4.695106s wall, 6.203125s user + 0.093750s system = 6.296875s CPU (134.1%)

OPT-1001 : Current memory(MB): used = 780, reserve = 771, peak = 809.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20697/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 908520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132911s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.0%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 51.48, top10 = 48.71, top15 = 46.83.
OPT-1001 : End congestion update;  0.446935s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (97.9%)

OPT-1001 : Update timing in Manhattan mode
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81765, tnet num: 22976, tinst num: 9155, tnode num: 110174, tedge num: 133671.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  2.099589s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (100.5%)

RUN-1004 : used memory is 688 MB, reserved memory is 677 MB, peak memory is 809 MB
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  2.962584s wall, 2.968750s user + 0.000000s system = 2.968750s CPU (100.2%)

OPT-0007 : Start: WNS 3370 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  3.414702s wall, 3.406250s user + 0.000000s system = 3.406250s CPU (99.8%)

OPT-1001 : Current memory(MB): used = 769, reserve = 760, peak = 809.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.836651s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20697/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 908520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131206s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.3%)

PHY-1001 : Congestion index: top1 = 55.95, top5 = 51.48, top10 = 48.71, top15 = 46.83.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.836627s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (99.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3370 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 55.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3370ps with logic level 8 
RUN-1001 :       #2 path slack 3434ps with logic level 8 
OPT-1001 : End physical optimization;  12.731557s wall, 14.218750s user + 0.109375s system = 14.328125s CPU (112.5%)

RUN-1003 : finish command "place" in  81.880104s wall, 136.812500s user + 7.234375s system = 144.046875s CPU (175.9%)

RUN-1004 : used memory is 654 MB, reserved memory is 642 MB, peak memory is 809 MB
RUN-1002 : start command "export_db INS600M-21A_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1003 : finish command "export_db INS600M-21A_place.db" in  1.694954s wall, 2.968750s user + 0.015625s system = 2.984375s CPU (176.1%)

RUN-1004 : used memory is 654 MB, reserved memory is 644 MB, peak memory is 809 MB
RUN-1002 : start command "route"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 9157 instances
RUN-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22978 nets
RUN-1001 : 16856 nets have 2 pins
RUN-1001 : 4508 nets have [3 - 5] pins
RUN-1001 : 1022 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 217 nets have [21 - 99] pins
RUN-1001 : 14 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model INS600M_21A.
TMR-2506 : Build timing graph completely. Port num: 41, tpin num: 81765, tnet num: 22976, tinst num: 9155, tnode num: 110174, tedge num: 133671.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer -report" in  1.855038s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (100.2%)

RUN-1004 : used memory is 650 MB, reserved memory is 639 MB, peak memory is 809 MB
PHY-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 811784, over cnt = 3334(9%), over = 5842, worst = 7
PHY-1002 : len = 834544, over cnt = 2382(6%), over = 3544, worst = 6
PHY-1002 : len = 867056, over cnt = 1037(2%), over = 1442, worst = 5
PHY-1002 : len = 886088, over cnt = 315(0%), over = 446, worst = 4
PHY-1002 : len = 894416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  2.394352s wall, 3.843750s user + 0.031250s system = 3.875000s CPU (161.8%)

PHY-1001 : Congestion index: top1 = 55.50, top5 = 51.17, top10 = 48.27, top15 = 46.33.
PHY-1001 : End global routing;  2.763484s wall, 4.203125s user + 0.046875s system = 4.250000s CPU (153.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 781, reserve = 775, peak = 809.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net COM3/GNRMC/u_fifo/ram_inst/clkb will be routed on clock mesh
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 1053, reserve = 1047, peak = 1053.
PHY-1001 : End build detailed router design. 4.447762s wall, 4.406250s user + 0.046875s system = 4.453125s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 199880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.843549s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 1091, reserve = 1086, peak = 1091.
PHY-1001 : End phase 1; 0.850814s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 57% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 79% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.14278e+06, over cnt = 3881(0%), over = 3939, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 1109, reserve = 1103, peak = 1109.
PHY-1001 : End initial routed; 22.408235s wall, 57.312500s user + 0.203125s system = 57.515625s CPU (256.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21749(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.102   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.692218s wall, 3.687500s user + 0.015625s system = 3.703125s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 1112, reserve = 1105, peak = 1112.
PHY-1001 : End phase 2; 26.100591s wall, 61.000000s user + 0.218750s system = 61.218750s CPU (234.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.14278e+06, over cnt = 3881(0%), over = 3939, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.280366s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.08677e+06, over cnt = 1765(0%), over = 1780, worst = 2, crit = 0
PHY-1001 : End DR Iter 1; 2.674628s wall, 3.437500s user + 0.015625s system = 3.453125s CPU (129.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.08417e+06, over cnt = 603(0%), over = 604, worst = 2, crit = 0
PHY-1001 : End DR Iter 2; 1.607443s wall, 1.953125s user + 0.062500s system = 2.015625s CPU (125.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.08952e+06, over cnt = 189(0%), over = 189, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.957956s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (107.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.09354e+06, over cnt = 71(0%), over = 71, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.562660s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (102.7%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.09598e+06, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.478088s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (101.3%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 2.09696e+06, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.651101s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (100.8%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 2.09766e+06, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 1.128910s wall, 1.140625s user + 0.000000s system = 1.140625s CPU (101.0%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 2.09768e+06, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.192777s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.3%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 2.09784e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.186646s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (100.5%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.09784e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.205703s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.7%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.09784e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.319646s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (107.5%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.09784e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.645186s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (104.1%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 2.09793e+06, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.184138s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.3%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.09794e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.182437s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.8%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 2.09793e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.214902s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.5%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.09785e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.282183s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.7%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.09785e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.513200s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (100.5%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.09785e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.678856s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (101.3%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 2.09794e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.182473s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.2%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.09794e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.185558s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.0%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.09794e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.241203s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.2%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.228557s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.5%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.415283s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.6%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.522797s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (104.6%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 1.297876s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.9%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.178219s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.4%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.176757s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.203248s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.9%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.257180s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.3%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.422350s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (118.4%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.511062s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (97.8%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 1.323527s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (101.5%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 1.424683s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (99.8%)

PHY-1001 : ===== DR Iter 34 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.175207s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.0%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.176776s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.218731s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.0%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.259493s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (120.4%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.407583s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.7%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.526427s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (100.9%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 1.298850s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.8%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 1.473936s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (99.6%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 1.412753s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.6%)

PHY-1001 : ===== DR Iter 43 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.181522s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (86.1%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.182951s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (93.9%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.211362s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (133.1%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.236290s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.2%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.410128s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.1%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.528956s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (100.4%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 1.342831s wall, 1.343750s user + 0.000000s system = 1.343750s CPU (100.1%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 1.404255s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (100.1%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 1.439902s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (99.8%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 1.407003s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (98.8%)

PHY-1001 : ===== DR Iter 53 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.177701s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (114.3%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.183792s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.0%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.211424s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.1%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.234486s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (100.0%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.414734s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.7%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.516995s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.7%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 1.299748s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.8%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 1.388912s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (100.1%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 1.391171s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (100.0%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 1.436112s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.1%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 1.444115s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (98.5%)

PHY-1001 : ===== DR Iter 64 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.176677s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.1%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.180223s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (138.7%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.201366s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.259815s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.2%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.404076s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.5%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.528412s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (100.5%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 1.301587s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.6%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 1.400393s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (100.4%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 1.424114s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (99.8%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 1.417004s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.3%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 1.409485s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.8%)

PHY-1001 : ==== DR Iter 75 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 75; 1.425095s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (99.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21749(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.102   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.653011s wall, 3.640625s user + 0.015625s system = 3.656250s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1128, reserve = 1123, peak = 1128.
PHY-1001 : End phase 3; 55.890266s wall, 57.218750s user + 0.250000s system = 57.468750s CPU (102.8%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.276627s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (101.7%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {0.102ns, 0.000ns, 0}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.172853s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.184789s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.222052s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (98.5%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.176413s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.4%)

PHY-1001 : ==== DR Iter 5 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.172977s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (108.4%)

PHY-1001 : ==== DR Iter 6 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.199490s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (109.7%)

PHY-1001 : ==== DR Iter 7 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.262010s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (119.3%)

PHY-1001 : ==== DR Iter 8 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.398343s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.0%)

PHY-1001 : ===== DR Iter 9 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.181511s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.7%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.181554s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.7%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.214636s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.9%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.229684s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.0%)

PHY-1001 : ==== DR Iter 13 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.394697s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.0%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.499995s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (100.0%)

PHY-1001 : ===== DR Iter 15 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.184345s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (110.2%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.191463s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (122.4%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.215757s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (101.4%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.228943s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (95.5%)

PHY-1001 : ==== DR Iter 19 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.407737s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.6%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.510004s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (104.2%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 1.299370s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.8%)

PHY-1001 : ===== DR Iter 22 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.177240s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.0%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.177703s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.195011s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.2%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.247774s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.9%)

PHY-1001 : ==== DR Iter 26 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.405814s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (111.7%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.535806s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (99.1%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 1.310101s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.2%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 1.407694s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.9%)

PHY-1001 : ===== DR Iter 30 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.171314s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (127.7%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.181093s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.2%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.199372s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (109.7%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.253364s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.7%)

PHY-1001 : ==== DR Iter 34 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.421540s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.1%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.562887s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.9%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 1.379787s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (98.5%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 1.481565s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.2%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 1.476976s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (100.5%)

PHY-1001 : ===== DR Iter 39 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.178722s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.2%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.182105s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.4%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.216219s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.2%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.225654s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (103.9%)

PHY-1001 : ==== DR Iter 43 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.404276s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (104.4%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.506310s wall, 0.531250s user + 0.031250s system = 0.562500s CPU (111.1%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 1.266521s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.9%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 1.383034s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (99.4%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 1.396402s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (100.7%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 1.381516s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (99.5%)

PHY-1001 : ===== DR Iter 49 =====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.189636s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (140.1%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.184948s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.4%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.09798e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.212034s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.8%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.228611s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.5%)

PHY-1001 : ==== DR Iter 53 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.425089s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (99.2%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.542354s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (100.8%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 1.293172s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.3%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 1.523212s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (99.5%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 1.433417s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.3%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 1.383846s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (99.4%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 1.408702s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.8%)

PHY-1001 : ===== DR Iter 60 =====
PHY-1022 : len = 2.09791e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.175270s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.1%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.180629s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.8%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.200574s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (116.9%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.252115s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.2%)

PHY-1001 : ==== DR Iter 64 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.412285s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (98.5%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.505901s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (101.9%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 1.266328s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.9%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 1.397519s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (99.5%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 1.418394s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.1%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 1.412165s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.6%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 1.431780s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.4%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.0979e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 1.432427s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (100.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21749(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.102   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.752551s wall, 3.734375s user + 0.000000s system = 3.734375s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 1130, reserve = 1124, peak = 1130.
PHY-1001 : End phase 4; 47.322602s wall, 47.484375s user + 0.250000s system = 47.734375s CPU (100.9%)

PHY-1003 : Routed, final wirelength = 2.0979e+06
PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1084 feed throughs used by 785 nets
PHY-1001 : Current memory(MB): used = 1235, reserve = 1233, peak = 1235.
PHY-1001 : End export database. 2.608421s wall, 2.609375s user + 0.000000s system = 2.609375s CPU (100.0%)

PHY-1001 : Fixing routing violation through ECO...
RUN-1002 : start command "place -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 12 thread(s).
PHY-1001 : Populate physical database on model INS600M_21A.
RUN-1001 : There are total 9157 instances
RUN-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22978 nets
RUN-1001 : 16856 nets have 2 pins
RUN-1001 : 4508 nets have [3 - 5] pins
RUN-1001 : 1022 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 217 nets have [21 - 99] pins
RUN-1001 : 14 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |      0      
RUN-1001 :   No   |  No   |  Yes  |      0      
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |      0      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    3    |   1   |     1      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 0
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 60 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: (1 18 2) is for feedthrough
PHY-3001 : eco cells: (2 20 0) is for feedthrough
PHY-3001 : eco cells: (2 24 3) is for feedthrough
PHY-3001 : eco cells: (2 48 3) is for feedthrough
PHY-3001 : eco cells: (2 63 0) is for feedthrough
PHY-3001 : eco cells: (2 64 2) is for feedthrough
PHY-3001 : eco cells: (2 66 1) is for feedthrough
PHY-3001 : eco cells: (2 69 1) is for feedthrough
PHY-3001 : eco cells: (3 22 1) is for feedthrough
PHY-3001 : eco cells: (3 23 1) is for feedthrough
PHY-3001 : eco cells: (3 24 2) is for feedthrough
PHY-3001 : eco cells: (3 25 1) is for feedthrough
PHY-3001 : eco cells: (3 29 2) is for feedthrough
PHY-3001 : eco cells: (3 39 0) is for feedthrough
PHY-3001 : eco cells: (3 46 2) is for feedthrough
PHY-3001 : eco cells: (3 52 2) is for feedthrough
PHY-3001 : eco cells: (3 55 2) is for feedthrough
PHY-3001 : eco cells: (3 56 2) is for feedthrough
PHY-3001 : eco cells: (3 62 2) is for feedthrough
PHY-3001 : eco cells: (3 63 2) is for feedthrough
PHY-3001 : eco cells: (3 64 1) is for feedthrough
PHY-3001 : eco cells: (3 64 2) is for feedthrough
PHY-3001 : eco cells: (3 65 0) is for feedthrough
PHY-3001 : eco cells: (3 65 1) is for feedthrough
PHY-3001 : eco cells: (3 68 0) is for feedthrough
PHY-3001 : eco cells: (3 70 3) is for feedthrough
PHY-3001 : eco cells: (4 15 3) is for feedthrough
PHY-3001 : eco cells: (4 17 0) is for feedthrough
PHY-3001 : eco cells: (4 21 2) is for feedthrough
PHY-3001 : eco cells: (4 21 3) is for feedthrough
PHY-3001 : eco cells: (4 22 1) is for feedthrough
PHY-3001 : eco cells: (4 26 0) is for feedthrough
PHY-3001 : eco cells: (4 27 1) is for feedthrough
PHY-3001 : eco cells: (4 31 0) is for feedthrough
PHY-3001 : eco cells: (4 51 3) is for feedthrough
PHY-3001 : eco cells: (4 54 2) is for feedthrough
PHY-3001 : eco cells: (4 54 3) is for feedthrough
PHY-3001 : eco cells: (4 66 1) is for feedthrough
PHY-3001 : eco cells: (4 67 0) is for feedthrough
PHY-3001 : eco cells: (4 67 1) is for feedthrough
PHY-3001 : eco cells: (4 67 3) is for feedthrough
PHY-3001 : eco cells: (4 68 0) is for feedthrough
PHY-3001 : eco cells: (4 68 1) is for feedthrough
PHY-3001 : eco cells: (5 6 0) is for feedthrough
PHY-3001 : eco cells: (5 9 0) is for feedthrough
PHY-3001 : eco cells: (5 13 0) is for feedthrough
PHY-3001 : eco cells: (5 14 3) is for feedthrough
PHY-3001 : eco cells: (5 17 2) is for feedthrough
PHY-3001 : eco cells: (5 17 3) is for feedthrough
PHY-3001 : eco cells: (5 18 2) is for feedthrough
PHY-3001 : eco cells: (5 18 3) is for feedthrough
PHY-3001 : eco cells: (5 21 0) is for feedthrough
PHY-3001 : eco cells: (5 23 0) is for feedthrough
PHY-3001 : eco cells: (5 23 2) is for feedthrough
PHY-3001 : eco cells: (5 24 3) is for feedthrough
PHY-3001 : eco cells: (5 27 2) is for feedthrough
PHY-3001 : eco cells: (5 31 2) is for feedthrough
PHY-3001 : eco cells: (5 31 3) is for feedthrough
PHY-3001 : eco cells: (5 39 1) is for feedthrough
PHY-3001 : eco cells: (5 39 3) is for feedthrough
PHY-3001 : eco cells: (5 40 1) is for feedthrough
PHY-3001 : eco cells: (5 49 3) is for feedthrough
PHY-3001 : eco cells: (5 50 0) is for feedthrough
PHY-3001 : eco cells: (5 50 1) is for feedthrough
PHY-3001 : eco cells: (5 61 3) is for feedthrough
PHY-3001 : eco cells: (5 65 0) is for feedthrough
PHY-3001 : eco cells: (5 65 1) is for feedthrough
PHY-3001 : eco cells: (6 9 1) is for feedthrough
PHY-3001 : eco cells: (6 17 1) is for feedthrough
PHY-3001 : eco cells: (6 19 3) is for feedthrough
PHY-3001 : eco cells: (6 20 2) is for feedthrough
PHY-3001 : eco cells: (6 23 1) is for feedthrough
PHY-3001 : eco cells: (6 26 1) is for feedthrough
PHY-3001 : eco cells: (6 27 2) is for feedthrough
PHY-3001 : eco cells: (6 29 0) is for feedthrough
PHY-3001 : eco cells: (6 30 0) is for feedthrough
PHY-3001 : eco cells: (6 30 1) is for feedthrough
PHY-3001 : eco cells: (6 30 3) is for feedthrough
PHY-3001 : eco cells: (6 34 2) is for feedthrough
PHY-3001 : eco cells: (6 43 2) is for feedthrough
PHY-3001 : eco cells: (6 54 0) is for feedthrough
PHY-3001 : eco cells: (6 63 2) is for feedthrough
PHY-3001 : eco cells: (6 64 0) is for feedthrough
PHY-3001 : eco cells: (6 65 1) is for feedthrough
PHY-3001 : eco cells: (6 67 1) is for feedthrough
PHY-3001 : eco cells: (7 3 3) is for feedthrough
PHY-3001 : eco cells: (7 4 2) is for feedthrough
PHY-3001 : eco cells: (7 4 3) is for feedthrough
PHY-3001 : eco cells: (7 10 0) is for feedthrough
PHY-3001 : eco cells: (7 19 3) is for feedthrough
PHY-3001 : eco cells: (7 24 1) is for feedthrough
PHY-3001 : eco cells: (7 25 0) is for feedthrough
PHY-3001 : eco cells: (7 27 2) is for feedthrough
PHY-3001 : eco cells: (7 28 1) is for feedthrough
PHY-3001 : eco cells: (7 30 1) is for feedthrough
PHY-3001 : eco cells: (7 31 3) is for feedthrough
PHY-3001 : eco cells: (7 57 3) is for feedthrough
PHY-3001 : eco cells: (7 62 3) is for feedthrough
PHY-3001 : eco cells: (7 65 1) is for feedthrough
PHY-3001 : eco cells: (7 66 1) is for feedthrough
PHY-3001 : eco cells: (7 68 0) is for feedthrough
PHY-3001 : eco cells: (9 24 2) is for feedthrough
PHY-3001 : eco cells: (9 43 2) is for feedthrough
PHY-3001 : eco cells: (9 58 2) is for feedthrough
PHY-3001 : eco cells: (9 65 0) is for feedthrough
PHY-3001 : eco cells: (10 3 0) is for feedthrough
PHY-3001 : eco cells: (10 4 0) is for feedthrough
PHY-3001 : eco cells: (10 7 3) is for feedthrough
PHY-3001 : eco cells: (10 20 2) is for feedthrough
PHY-3001 : eco cells: (10 24 1) is for feedthrough
PHY-3001 : eco cells: (10 24 2) is for feedthrough
PHY-3001 : eco cells: (10 27 2) is for feedthrough
PHY-3001 : eco cells: (10 28 1) is for feedthrough
PHY-3001 : eco cells: (10 32 0) is for feedthrough
PHY-3001 : eco cells: (10 32 2) is for feedthrough
PHY-3001 : eco cells: (10 34 2) is for feedthrough
PHY-3001 : eco cells: (10 35 3) is for feedthrough
PHY-3001 : eco cells: (10 37 2) is for feedthrough
PHY-3001 : eco cells: (10 46 0) is for feedthrough
PHY-3001 : eco cells: (10 61 3) is for feedthrough
PHY-3001 : eco cells: (10 63 2) is for feedthrough
PHY-3001 : eco cells: (11 10 0) is for feedthrough
PHY-3001 : eco cells: (11 15 2) is for feedthrough
PHY-3001 : eco cells: (11 26 1) is for feedthrough
PHY-3001 : eco cells: (11 27 3) is for feedthrough
PHY-3001 : eco cells: (11 42 1) is for feedthrough
PHY-3001 : eco cells: (11 43 1) is for feedthrough
PHY-3001 : eco cells: (11 49 3) is for feedthrough
PHY-3001 : eco cells: (11 69 3) is for feedthrough
PHY-3001 : eco cells: (12 3 1) is for feedthrough
PHY-3001 : eco cells: (12 7 2) is for feedthrough
PHY-3001 : eco cells: (12 9 3) is for feedthrough
PHY-3001 : eco cells: (12 19 1) is for feedthrough
PHY-3001 : eco cells: (12 23 2) is for feedthrough
PHY-3001 : eco cells: (12 24 0) is for feedthrough
PHY-3001 : eco cells: (12 27 1) is for feedthrough
PHY-3001 : eco cells: (12 28 0) is for feedthrough
PHY-3001 : eco cells: (12 30 3) is for feedthrough
PHY-3001 : eco cells: (12 31 3) is for feedthrough
PHY-3001 : eco cells: (12 41 1) is for feedthrough
PHY-3001 : eco cells: (12 66 2) is for feedthrough
PHY-3001 : eco cells: (13 3 0) is for feedthrough
PHY-3001 : eco cells: (13 5 0) is for feedthrough
PHY-3001 : eco cells: (13 5 2) is for feedthrough
PHY-3001 : eco cells: (13 6 1) is for feedthrough
PHY-3001 : eco cells: (13 7 0) is for feedthrough
PHY-3001 : eco cells: (13 7 1) is for feedthrough
PHY-3001 : eco cells: (13 8 3) is for feedthrough
PHY-3001 : eco cells: (13 18 3) is for feedthrough
PHY-3001 : eco cells: (13 23 0) is for feedthrough
PHY-3001 : eco cells: (13 23 1) is for feedthrough
PHY-3001 : eco cells: (13 24 0) is for feedthrough
PHY-3001 : eco cells: (13 25 1) is for feedthrough
PHY-3001 : eco cells: (13 27 0) is for feedthrough
PHY-3001 : eco cells: (13 30 3) is for feedthrough
PHY-3001 : eco cells: (13 31 3) is for feedthrough
PHY-3001 : eco cells: (14 4 2) is for feedthrough
PHY-3001 : eco cells: (14 5 1) is for feedthrough
PHY-3001 : eco cells: (14 6 0) is for feedthrough
PHY-3001 : eco cells: (14 8 3) is for feedthrough
PHY-3001 : eco cells: (14 22 1) is for feedthrough
PHY-3001 : eco cells: (14 23 1) is for feedthrough
PHY-3001 : eco cells: (14 26 0) is for feedthrough
PHY-3001 : eco cells: (15 3 2) is for feedthrough
PHY-3001 : eco cells: (15 15 2) is for feedthrough
PHY-3001 : eco cells: (15 18 3) is for feedthrough
PHY-3001 : eco cells: (15 19 2) is for feedthrough
PHY-3001 : eco cells: (15 23 1) is for feedthrough
PHY-3001 : eco cells: (15 26 3) is for feedthrough
PHY-3001 : eco cells: (15 29 0) is for feedthrough
PHY-3001 : eco cells: (17 3 1) is for feedthrough
PHY-3001 : eco cells: (17 6 0) is for feedthrough
PHY-3001 : eco cells: (17 6 1) is for feedthrough
PHY-3001 : eco cells: (17 14 1) is for feedthrough
PHY-3001 : eco cells: (17 16 1) is for feedthrough
PHY-3001 : eco cells: (17 17 0) is for feedthrough
PHY-3001 : eco cells: (17 17 1) is for feedthrough
PHY-3001 : eco cells: (17 18 0) is for feedthrough
PHY-3001 : eco cells: (17 19 2) is for feedthrough
PHY-3001 : eco cells: (17 20 0) is for feedthrough
PHY-3001 : eco cells: (17 22 0) is for feedthrough
PHY-3001 : eco cells: (17 23 0) is for feedthrough
PHY-3001 : eco cells: (17 25 0) is for feedthrough
PHY-3001 : eco cells: (17 26 2) is for feedthrough
PHY-3001 : eco cells: (17 26 3) is for feedthrough
PHY-3001 : eco cells: (17 30 0) is for feedthrough
PHY-3001 : eco cells: (17 31 0) is for feedthrough
PHY-3001 : eco cells: (17 58 1) is for feedthrough
PHY-3001 : eco cells: (18 6 1) is for feedthrough
PHY-3001 : eco cells: (18 7 0) is for feedthrough
PHY-3001 : eco cells: (18 7 1) is for feedthrough
PHY-3001 : eco cells: (18 8 3) is for feedthrough
PHY-3001 : eco cells: (18 9 0) is for feedthrough
PHY-3001 : eco cells: (18 12 2) is for feedthrough
PHY-3001 : eco cells: (18 14 1) is for feedthrough
PHY-3001 : eco cells: (18 14 2) is for feedthrough
PHY-3001 : eco cells: (18 15 0) is for feedthrough
PHY-3001 : eco cells: (18 15 2) is for feedthrough
PHY-3001 : eco cells: (18 16 0) is for feedthrough
PHY-3001 : eco cells: (18 17 1) is for feedthrough
PHY-3001 : eco cells: (18 22 1) is for feedthrough
PHY-3001 : eco cells: (18 23 1) is for feedthrough
PHY-3001 : eco cells: (18 24 3) is for feedthrough
PHY-3001 : eco cells: (18 25 0) is for feedthrough
PHY-3001 : eco cells: (18 58 3) is for feedthrough
PHY-3001 : eco cells: (19 2 0) is for feedthrough
PHY-3001 : eco cells: (19 3 1) is for feedthrough
PHY-3001 : eco cells: (19 4 0) is for feedthrough
PHY-3001 : eco cells: (19 5 1) is for feedthrough
PHY-3001 : eco cells: (19 5 2) is for feedthrough
PHY-3001 : eco cells: (19 6 1) is for feedthrough
PHY-3001 : eco cells: (19 7 0) is for feedthrough
PHY-3001 : eco cells: (19 7 1) is for feedthrough
PHY-3001 : eco cells: (19 8 0) is for feedthrough
PHY-3001 : eco cells: (19 9 0) is for feedthrough
PHY-3001 : eco cells: (19 10 1) is for feedthrough
PHY-3001 : eco cells: (19 11 1) is for feedthrough
PHY-3001 : eco cells: (19 12 0) is for feedthrough
PHY-3001 : eco cells: (19 12 1) is for feedthrough
PHY-3001 : eco cells: (19 13 0) is for feedthrough
PHY-3001 : eco cells: (19 16 0) is for feedthrough
PHY-3001 : eco cells: (19 18 2) is for feedthrough
PHY-3001 : eco cells: (19 22 1) is for feedthrough
PHY-3001 : eco cells: (19 25 0) is for feedthrough
PHY-3001 : eco cells: (19 34 3) is for feedthrough
PHY-3001 : eco cells: (19 40 3) is for feedthrough
PHY-3001 : eco cells: (19 53 2) is for feedthrough
PHY-3001 : eco cells: (20 3 1) is for feedthrough
PHY-3001 : eco cells: (20 3 2) is for feedthrough
PHY-3001 : eco cells: (20 3 3) is for feedthrough
PHY-3001 : eco cells: (20 4 1) is for feedthrough
PHY-3001 : eco cells: (20 4 2) is for feedthrough
PHY-3001 : eco cells: (20 5 1) is for feedthrough
PHY-3001 : eco cells: (20 6 0) is for feedthrough
PHY-3001 : eco cells: (20 6 1) is for feedthrough
PHY-3001 : eco cells: (20 7 0) is for feedthrough
PHY-3001 : eco cells: (20 7 1) is for feedthrough
PHY-3001 : eco cells: (20 8 0) is for feedthrough
PHY-3001 : eco cells: (20 8 1) is for feedthrough
PHY-3001 : eco cells: (20 9 1) is for feedthrough
PHY-3001 : eco cells: (20 9 2) is for feedthrough
PHY-3001 : eco cells: (20 10 1) is for feedthrough
PHY-3001 : eco cells: (20 11 0) is for feedthrough
PHY-3001 : eco cells: (20 11 1) is for feedthrough
PHY-3001 : eco cells: (20 12 0) is for feedthrough
PHY-3001 : eco cells: (20 12 2) is for feedthrough
PHY-3001 : eco cells: (20 13 1) is for feedthrough
PHY-3001 : eco cells: (20 14 0) is for feedthrough
PHY-3001 : eco cells: (20 16 3) is for feedthrough
PHY-3001 : eco cells: (20 17 2) is for feedthrough
PHY-3001 : eco cells: (20 18 0) is for feedthrough
PHY-3001 : eco cells: (20 18 1) is for feedthrough
PHY-3001 : eco cells: (20 19 2) is for feedthrough
PHY-3001 : eco cells: (20 21 3) is for feedthrough
PHY-3001 : eco cells: (20 25 0) is for feedthrough
PHY-3001 : eco cells: (20 25 1) is for feedthrough
PHY-3001 : eco cells: (20 26 3) is for feedthrough
PHY-3001 : eco cells: (20 27 0) is for feedthrough
PHY-3001 : eco cells: (20 31 1) is for feedthrough
PHY-3001 : eco cells: (20 35 1) is for feedthrough
PHY-3001 : eco cells: (20 37 3) is for feedthrough
PHY-3001 : eco cells: (20 39 2) is for feedthrough
PHY-3001 : eco cells: (20 58 1) is for feedthrough
PHY-3001 : eco cells: (20 65 2) is for feedthrough
PHY-3001 : eco cells: (20 68 0) is for feedthrough
PHY-3001 : eco cells: (21 3 1) is for feedthrough
PHY-3001 : eco cells: (21 4 1) is for feedthrough
PHY-3001 : eco cells: (21 5 0) is for feedthrough
PHY-3001 : eco cells: (21 5 1) is for feedthrough
PHY-3001 : eco cells: (21 5 2) is for feedthrough
PHY-3001 : eco cells: (21 6 0) is for feedthrough
PHY-3001 : eco cells: (21 6 1) is for feedthrough
PHY-3001 : eco cells: (21 7 3) is for feedthrough
PHY-3001 : eco cells: (21 8 1) is for feedthrough
PHY-3001 : eco cells: (21 8 3) is for feedthrough
PHY-3001 : eco cells: (21 9 0) is for feedthrough
PHY-3001 : eco cells: (21 10 0) is for feedthrough
PHY-3001 : eco cells: (21 10 1) is for feedthrough
PHY-3001 : eco cells: (21 11 1) is for feedthrough
PHY-3001 : eco cells: (21 12 0) is for feedthrough
PHY-3001 : eco cells: (21 12 2) is for feedthrough
PHY-3001 : eco cells: (21 13 0) is for feedthrough
PHY-3001 : eco cells: (21 13 2) is for feedthrough
PHY-3001 : eco cells: (21 16 1) is for feedthrough
PHY-3001 : eco cells: (21 16 2) is for feedthrough
PHY-3001 : eco cells: (21 16 3) is for feedthrough
PHY-3001 : eco cells: (21 17 0) is for feedthrough
PHY-3001 : eco cells: (21 17 2) is for feedthrough
PHY-3001 : eco cells: (21 19 0) is for feedthrough
PHY-3001 : eco cells: (21 19 1) is for feedthrough
PHY-3001 : eco cells: (21 19 2) is for feedthrough
PHY-3001 : eco cells: (21 19 3) is for feedthrough
PHY-3001 : eco cells: (21 20 3) is for feedthrough
PHY-3001 : eco cells: (21 21 3) is for feedthrough
PHY-3001 : eco cells: (21 22 0) is for feedthrough
PHY-3001 : eco cells: (21 22 1) is for feedthrough
PHY-3001 : eco cells: (21 23 1) is for feedthrough
PHY-3001 : eco cells: (21 24 1) is for feedthrough
PHY-3001 : eco cells: (21 24 3) is for feedthrough
PHY-3001 : eco cells: (21 27 0) is for feedthrough
PHY-3001 : eco cells: (21 66 3) is for feedthrough
PHY-3001 : eco cells: (22 1 0) is for feedthrough
PHY-3001 : eco cells: (22 2 0) is for feedthrough
PHY-3001 : eco cells: (22 4 0) is for feedthrough
PHY-3001 : eco cells: (22 4 1) is for feedthrough
PHY-3001 : eco cells: (22 5 0) is for feedthrough
PHY-3001 : eco cells: (22 6 1) is for feedthrough
PHY-3001 : eco cells: (22 7 1) is for feedthrough
PHY-3001 : eco cells: (22 8 0) is for feedthrough
PHY-3001 : eco cells: (22 9 1) is for feedthrough
PHY-3001 : eco cells: (22 10 0) is for feedthrough
PHY-3001 : eco cells: (22 10 1) is for feedthrough
PHY-3001 : eco cells: (22 11 0) is for feedthrough
PHY-3001 : eco cells: (22 11 2) is for feedthrough
PHY-3001 : eco cells: (22 11 3) is for feedthrough
PHY-3001 : eco cells: (22 12 0) is for feedthrough
PHY-3001 : eco cells: (22 12 1) is for feedthrough
PHY-3001 : eco cells: (22 13 1) is for feedthrough
PHY-3001 : eco cells: (22 14 1) is for feedthrough
PHY-3001 : eco cells: (22 15 1) is for feedthrough
PHY-3001 : eco cells: (22 15 2) is for feedthrough
PHY-3001 : eco cells: (22 16 0) is for feedthrough
PHY-3001 : eco cells: (22 17 1) is for feedthrough
PHY-3001 : eco cells: (22 17 2) is for feedthrough
PHY-3001 : eco cells: (22 19 0) is for feedthrough
PHY-3001 : eco cells: (22 20 2) is for feedthrough
PHY-3001 : eco cells: (22 25 1) is for feedthrough
PHY-3001 : eco cells: (22 51 1) is for feedthrough
PHY-3001 : eco cells: (22 61 3) is for feedthrough
PHY-3001 : eco cells: (22 69 2) is for feedthrough
PHY-3001 : eco cells: (23 2 1) is for feedthrough
PHY-3001 : eco cells: (23 3 0) is for feedthrough
PHY-3001 : eco cells: (23 4 1) is for feedthrough
PHY-3001 : eco cells: (23 5 0) is for feedthrough
PHY-3001 : eco cells: (23 7 1) is for feedthrough
PHY-3001 : eco cells: (23 8 0) is for feedthrough
PHY-3001 : eco cells: (23 8 1) is for feedthrough
PHY-3001 : eco cells: (23 9 0) is for feedthrough
PHY-3001 : eco cells: (23 9 1) is for feedthrough
PHY-3001 : eco cells: (23 9 2) is for feedthrough
PHY-3001 : eco cells: (23 10 0) is for feedthrough
PHY-3001 : eco cells: (23 10 3) is for feedthrough
PHY-3001 : eco cells: (23 11 0) is for feedthrough
PHY-3001 : eco cells: (23 12 0) is for feedthrough
PHY-3001 : eco cells: (23 12 1) is for feedthrough
PHY-3001 : eco cells: (23 13 0) is for feedthrough
PHY-3001 : eco cells: (23 13 1) is for feedthrough
PHY-3001 : eco cells: (23 13 2) is for feedthrough
PHY-3001 : eco cells: (23 14 1) is for feedthrough
PHY-3001 : eco cells: (23 14 3) is for feedthrough
PHY-3001 : eco cells: (23 15 3) is for feedthrough
PHY-3001 : eco cells: (23 16 0) is for feedthrough
PHY-3001 : eco cells: (23 16 1) is for feedthrough
PHY-3001 : eco cells: (23 17 0) is for feedthrough
PHY-3001 : eco cells: (23 19 2) is for feedthrough
PHY-3001 : eco cells: (23 20 2) is for feedthrough
PHY-3001 : eco cells: (23 21 2) is for feedthrough
PHY-3001 : eco cells: (23 22 1) is for feedthrough
PHY-3001 : eco cells: (23 23 0) is for feedthrough
PHY-3001 : eco cells: (23 23 1) is for feedthrough
PHY-3001 : eco cells: (23 25 2) is for feedthrough
PHY-3001 : eco cells: (23 26 0) is for feedthrough
PHY-3001 : eco cells: (23 33 1) is for feedthrough
PHY-3001 : eco cells: (25 5 0) is for feedthrough
PHY-3001 : eco cells: (25 6 1) is for feedthrough
PHY-3001 : eco cells: (25 6 2) is for feedthrough
PHY-3001 : eco cells: (25 7 0) is for feedthrough
PHY-3001 : eco cells: (25 7 3) is for feedthrough
PHY-3001 : eco cells: (25 8 1) is for feedthrough
PHY-3001 : eco cells: (25 8 2) is for feedthrough
PHY-3001 : eco cells: (25 9 1) is for feedthrough
PHY-3001 : eco cells: (25 10 0) is for feedthrough
PHY-3001 : eco cells: (25 10 1) is for feedthrough
PHY-3001 : eco cells: (25 11 0) is for feedthrough
PHY-3001 : eco cells: (25 11 1) is for feedthrough
PHY-3001 : eco cells: (25 11 2) is for feedthrough
PHY-3001 : eco cells: (25 12 1) is for feedthrough
PHY-3001 : eco cells: (25 12 2) is for feedthrough
PHY-3001 : eco cells: (25 12 3) is for feedthrough
PHY-3001 : eco cells: (25 13 0) is for feedthrough
PHY-3001 : eco cells: (25 13 2) is for feedthrough
PHY-3001 : eco cells: (25 14 3) is for feedthrough
PHY-3001 : eco cells: (25 15 1) is for feedthrough
PHY-3001 : eco cells: (25 15 2) is for feedthrough
PHY-3001 : eco cells: (25 16 0) is for feedthrough
PHY-3001 : eco cells: (25 16 1) is for feedthrough
PHY-3001 : eco cells: (25 17 1) is for feedthrough
PHY-3001 : eco cells: (25 18 2) is for feedthrough
PHY-3001 : eco cells: (25 20 0) is for feedthrough
PHY-3001 : eco cells: (25 21 0) is for feedthrough
PHY-3001 : eco cells: (25 22 0) is for feedthrough
PHY-3001 : eco cells: (25 26 0) is for feedthrough
PHY-3001 : eco cells: (25 27 2) is for feedthrough
PHY-3001 : eco cells: (25 31 3) is for feedthrough
PHY-3001 : eco cells: (25 35 0) is for feedthrough
PHY-3001 : eco cells: (25 38 2) is for feedthrough
PHY-3001 : eco cells: (25 43 2) is for feedthrough
PHY-3001 : eco cells: (25 43 3) is for feedthrough
PHY-3001 : eco cells: (25 44 0) is for feedthrough
PHY-3001 : eco cells: (25 48 3) is for feedthrough
PHY-3001 : eco cells: (26 2 0) is for feedthrough
PHY-3001 : eco cells: (26 2 1) is for feedthrough
PHY-3001 : eco cells: (26 3 0) is for feedthrough
PHY-3001 : eco cells: (26 3 1) is for feedthrough
PHY-3001 : eco cells: (26 3 2) is for feedthrough
PHY-3001 : eco cells: (26 4 1) is for feedthrough
PHY-3001 : eco cells: (26 5 0) is for feedthrough
PHY-3001 : eco cells: (26 5 2) is for feedthrough
PHY-3001 : eco cells: (26 6 1) is for feedthrough
PHY-3001 : eco cells: (26 8 0) is for feedthrough
PHY-3001 : eco cells: (26 8 3) is for feedthrough
PHY-3001 : eco cells: (26 9 0) is for feedthrough
PHY-3001 : eco cells: (26 11 3) is for feedthrough
PHY-3001 : eco cells: (26 12 0) is for feedthrough
PHY-3001 : eco cells: (26 12 3) is for feedthrough
PHY-3001 : eco cells: (26 13 3) is for feedthrough
PHY-3001 : eco cells: (26 16 2) is for feedthrough
PHY-3001 : eco cells: (26 17 0) is for feedthrough
PHY-3001 : eco cells: (26 17 1) is for feedthrough
PHY-3001 : eco cells: (26 17 2) is for feedthrough
PHY-3001 : eco cells: (26 18 1) is for feedthrough
PHY-3001 : eco cells: (26 19 0) is for feedthrough
PHY-3001 : eco cells: (26 20 1) is for feedthrough
PHY-3001 : eco cells: (26 22 1) is for feedthrough
PHY-3001 : eco cells: (26 22 3) is for feedthrough
PHY-3001 : eco cells: (26 23 0) is for feedthrough
PHY-3001 : eco cells: (26 25 1) is for feedthrough
PHY-3001 : eco cells: (26 29 1) is for feedthrough
PHY-3001 : eco cells: (26 44 2) is for feedthrough
PHY-3001 : eco cells: (26 45 3) is for feedthrough
PHY-3001 : eco cells: (26 61 1) is for feedthrough
PHY-3001 : eco cells: (27 1 0) is for feedthrough
PHY-3001 : eco cells: (27 2 0) is for feedthrough
PHY-3001 : eco cells: (27 2 1) is for feedthrough
PHY-3001 : eco cells: (27 3 1) is for feedthrough
PHY-3001 : eco cells: (27 4 1) is for feedthrough
PHY-3001 : eco cells: (27 7 0) is for feedthrough
PHY-3001 : eco cells: (27 8 0) is for feedthrough
PHY-3001 : eco cells: (27 9 1) is for feedthrough
PHY-3001 : eco cells: (27 12 0) is for feedthrough
PHY-3001 : eco cells: (27 12 1) is for feedthrough
PHY-3001 : eco cells: (27 12 2) is for feedthrough
PHY-3001 : eco cells: (27 13 0) is for feedthrough
PHY-3001 : eco cells: (27 13 2) is for feedthrough
PHY-3001 : eco cells: (27 15 2) is for feedthrough
PHY-3001 : eco cells: (27 16 1) is for feedthrough
PHY-3001 : eco cells: (27 17 2) is for feedthrough
PHY-3001 : eco cells: (27 17 3) is for feedthrough
PHY-3001 : eco cells: (27 18 2) is for feedthrough
PHY-3001 : eco cells: (27 19 1) is for feedthrough
PHY-3001 : eco cells: (27 21 1) is for feedthrough
PHY-3001 : eco cells: (27 21 2) is for feedthrough
PHY-3001 : eco cells: (27 22 2) is for feedthrough
PHY-3001 : eco cells: (27 23 1) is for feedthrough
PHY-3001 : eco cells: (27 24 0) is for feedthrough
PHY-3001 : eco cells: (27 25 3) is for feedthrough
PHY-3001 : eco cells: (27 27 3) is for feedthrough
PHY-3001 : eco cells: (27 28 2) is for feedthrough
PHY-3001 : eco cells: (27 32 0) is for feedthrough
PHY-3001 : eco cells: (27 39 2) is for feedthrough
PHY-3001 : eco cells: (27 62 0) is for feedthrough
PHY-3001 : eco cells: (27 64 1) is for feedthrough
PHY-3001 : eco cells: (27 65 1) is for feedthrough
PHY-3001 : eco cells: (27 65 3) is for feedthrough
PHY-3001 : eco cells: (28 1 1) is for feedthrough
PHY-3001 : eco cells: (28 2 0) is for feedthrough
PHY-3001 : eco cells: (28 3 0) is for feedthrough
PHY-3001 : eco cells: (28 4 1) is for feedthrough
PHY-3001 : eco cells: (28 5 3) is for feedthrough
PHY-3001 : eco cells: (28 6 0) is for feedthrough
PHY-3001 : eco cells: (28 7 0) is for feedthrough
PHY-3001 : eco cells: (28 7 1) is for feedthrough
PHY-3001 : eco cells: (28 7 2) is for feedthrough
PHY-3001 : eco cells: (28 8 0) is for feedthrough
PHY-3001 : eco cells: (28 8 1) is for feedthrough
PHY-3001 : eco cells: (28 9 3) is for feedthrough
PHY-3001 : eco cells: (28 10 2) is for feedthrough
PHY-3001 : eco cells: (28 11 0) is for feedthrough
PHY-3001 : eco cells: (28 11 1) is for feedthrough
PHY-3001 : eco cells: (28 12 0) is for feedthrough
PHY-3001 : eco cells: (28 15 1) is for feedthrough
PHY-3001 : eco cells: (28 20 0) is for feedthrough
PHY-3001 : eco cells: (28 20 1) is for feedthrough
PHY-3001 : eco cells: (28 20 2) is for feedthrough
PHY-3001 : eco cells: (28 21 0) is for feedthrough
PHY-3001 : eco cells: (28 22 0) is for feedthrough
PHY-3001 : eco cells: (28 23 2) is for feedthrough
PHY-3001 : eco cells: (28 39 0) is for feedthrough
PHY-3001 : eco cells: (28 49 0) is for feedthrough
PHY-3001 : eco cells: (28 53 3) is for feedthrough
PHY-3001 : eco cells: (28 55 1) is for feedthrough
PHY-3001 : eco cells: (29 1 0) is for feedthrough
PHY-3001 : eco cells: (29 2 1) is for feedthrough
PHY-3001 : eco cells: (29 3 0) is for feedthrough
PHY-3001 : eco cells: (29 3 1) is for feedthrough
PHY-3001 : eco cells: (29 4 0) is for feedthrough
PHY-3001 : eco cells: (29 4 1) is for feedthrough
PHY-3001 : eco cells: (29 5 0) is for feedthrough
PHY-3001 : eco cells: (29 5 1) is for feedthrough
PHY-3001 : eco cells: (29 5 2) is for feedthrough
PHY-3001 : eco cells: (29 6 0) is for feedthrough
PHY-3001 : eco cells: (29 7 1) is for feedthrough
PHY-3001 : eco cells: (29 8 0) is for feedthrough
PHY-3001 : eco cells: (29 9 0) is for feedthrough
PHY-3001 : eco cells: (29 9 1) is for feedthrough
PHY-3001 : eco cells: (29 10 0) is for feedthrough
PHY-3001 : eco cells: (29 10 1) is for feedthrough
PHY-3001 : eco cells: (29 10 2) is for feedthrough
PHY-3001 : eco cells: (29 10 3) is for feedthrough
PHY-3001 : eco cells: (29 11 1) is for feedthrough
PHY-3001 : eco cells: (29 11 2) is for feedthrough
PHY-3001 : eco cells: (29 12 1) is for feedthrough
PHY-3001 : eco cells: (29 13 0) is for feedthrough
PHY-3001 : eco cells: (29 13 1) is for feedthrough
PHY-3001 : eco cells: (29 14 0) is for feedthrough
PHY-3001 : eco cells: (29 17 2) is for feedthrough
PHY-3001 : eco cells: (29 19 1) is for feedthrough
PHY-3001 : eco cells: (29 20 0) is for feedthrough
PHY-3001 : eco cells: (29 20 1) is for feedthrough
PHY-3001 : eco cells: (29 20 3) is for feedthrough
PHY-3001 : eco cells: (29 21 0) is for feedthrough
PHY-3001 : eco cells: (29 21 1) is for feedthrough
PHY-3001 : eco cells: (29 38 0) is for feedthrough
PHY-3001 : eco cells: (29 41 3) is for feedthrough
PHY-3001 : eco cells: (29 58 1) is for feedthrough
PHY-3001 : eco cells: (29 61 1) is for feedthrough
PHY-3001 : eco cells: (29 64 0) is for feedthrough
PHY-3001 : eco cells: (30 1 1) is for feedthrough
PHY-3001 : eco cells: (30 2 0) is for feedthrough
PHY-3001 : eco cells: (30 4 1) is for feedthrough
PHY-3001 : eco cells: (30 5 0) is for feedthrough
PHY-3001 : eco cells: (30 5 1) is for feedthrough
PHY-3001 : eco cells: (30 7 0) is for feedthrough
PHY-3001 : eco cells: (30 9 0) is for feedthrough
PHY-3001 : eco cells: (30 9 2) is for feedthrough
PHY-3001 : eco cells: (30 10 0) is for feedthrough
PHY-3001 : eco cells: (30 10 2) is for feedthrough
PHY-3001 : eco cells: (30 11 0) is for feedthrough
PHY-3001 : eco cells: (30 11 2) is for feedthrough
PHY-3001 : eco cells: (30 12 0) is for feedthrough
PHY-3001 : eco cells: (30 12 1) is for feedthrough
PHY-3001 : eco cells: (30 12 2) is for feedthrough
PHY-3001 : eco cells: (30 13 0) is for feedthrough
PHY-3001 : eco cells: (30 14 1) is for feedthrough
PHY-3001 : eco cells: (30 15 1) is for feedthrough
PHY-3001 : eco cells: (30 16 0) is for feedthrough
PHY-3001 : eco cells: (30 17 0) is for feedthrough
PHY-3001 : eco cells: (30 17 1) is for feedthrough
PHY-3001 : eco cells: (30 19 0) is for feedthrough
PHY-3001 : eco cells: (30 19 1) is for feedthrough
PHY-3001 : eco cells: (30 20 0) is for feedthrough
PHY-3001 : eco cells: (30 20 1) is for feedthrough
PHY-3001 : eco cells: (30 20 2) is for feedthrough
PHY-3001 : eco cells: (30 23 3) is for feedthrough
PHY-3001 : eco cells: (30 28 2) is for feedthrough
PHY-3001 : eco cells: (30 34 2) is for feedthrough
PHY-3001 : eco cells: (30 40 1) is for feedthrough
PHY-3001 : eco cells: (30 54 0) is for feedthrough
PHY-3001 : eco cells: (30 59 3) is for feedthrough
PHY-3001 : eco cells: (30 61 3) is for feedthrough
PHY-3001 : eco cells: (30 66 2) is for feedthrough
PHY-3001 : eco cells: (31 2 1) is for feedthrough
PHY-3001 : eco cells: (31 3 1) is for feedthrough
PHY-3001 : eco cells: (31 4 0) is for feedthrough
PHY-3001 : eco cells: (31 5 1) is for feedthrough
PHY-3001 : eco cells: (31 6 0) is for feedthrough
PHY-3001 : eco cells: (31 6 2) is for feedthrough
PHY-3001 : eco cells: (31 7 0) is for feedthrough
PHY-3001 : eco cells: (31 7 1) is for feedthrough
PHY-3001 : eco cells: (31 9 0) is for feedthrough
PHY-3001 : eco cells: (31 9 3) is for feedthrough
PHY-3001 : eco cells: (31 10 0) is for feedthrough
PHY-3001 : eco cells: (31 10 1) is for feedthrough
PHY-3001 : eco cells: (31 11 1) is for feedthrough
PHY-3001 : eco cells: (31 13 1) is for feedthrough
PHY-3001 : eco cells: (31 13 2) is for feedthrough
PHY-3001 : eco cells: (31 14 1) is for feedthrough
PHY-3001 : eco cells: (31 16 0) is for feedthrough
PHY-3001 : eco cells: (31 17 0) is for feedthrough
PHY-3001 : eco cells: (31 17 1) is for feedthrough
PHY-3001 : eco cells: (31 18 1) is for feedthrough
PHY-3001 : eco cells: (31 19 1) is for feedthrough
PHY-3001 : eco cells: (31 19 2) is for feedthrough
PHY-3001 : eco cells: (31 20 0) is for feedthrough
PHY-3001 : eco cells: (31 21 0) is for feedthrough
PHY-3001 : eco cells: (31 25 1) is for feedthrough
PHY-3001 : eco cells: (31 26 0) is for feedthrough
PHY-3001 : eco cells: (31 28 3) is for feedthrough
PHY-3001 : eco cells: (31 31 1) is for feedthrough
PHY-3001 : eco cells: (31 39 0) is for feedthrough
PHY-3001 : eco cells: (31 59 0) is for feedthrough
PHY-3001 : eco cells: (31 65 1) is for feedthrough
PHY-3001 : eco cells: (33 2 2) is for feedthrough
PHY-3001 : eco cells: (33 4 2) is for feedthrough
PHY-3001 : eco cells: (33 5 0) is for feedthrough
PHY-3001 : eco cells: (33 5 1) is for feedthrough
PHY-3001 : eco cells: (33 7 0) is for feedthrough
PHY-3001 : eco cells: (33 8 1) is for feedthrough
PHY-3001 : eco cells: (33 9 0) is for feedthrough
PHY-3001 : eco cells: (33 10 0) is for feedthrough
PHY-3001 : eco cells: (33 10 1) is for feedthrough
PHY-3001 : eco cells: (33 10 3) is for feedthrough
PHY-3001 : eco cells: (33 11 0) is for feedthrough
PHY-3001 : eco cells: (33 11 1) is for feedthrough
PHY-3001 : eco cells: (33 11 2) is for feedthrough
PHY-3001 : eco cells: (33 15 1) is for feedthrough
PHY-3001 : eco cells: (33 16 0) is for feedthrough
PHY-3001 : eco cells: (33 17 1) is for feedthrough
PHY-3001 : eco cells: (33 18 0) is for feedthrough
PHY-3001 : eco cells: (33 18 1) is for feedthrough
PHY-3001 : eco cells: (33 19 0) is for feedthrough
PHY-3001 : eco cells: (33 19 1) is for feedthrough
PHY-3001 : eco cells: (33 21 2) is for feedthrough
PHY-3001 : eco cells: (33 37 0) is for feedthrough
PHY-3001 : eco cells: (33 39 0) is for feedthrough
PHY-3001 : eco cells: (33 39 2) is for feedthrough
PHY-3001 : eco cells: (33 40 3) is for feedthrough
PHY-3001 : eco cells: (33 42 1) is for feedthrough
PHY-3001 : eco cells: (33 46 3) is for feedthrough
PHY-3001 : eco cells: (33 58 3) is for feedthrough
PHY-3001 : eco cells: (33 59 2) is for feedthrough
PHY-3001 : eco cells: (33 60 1) is for feedthrough
PHY-3001 : eco cells: (33 62 3) is for feedthrough
PHY-3001 : eco cells: (33 64 1) is for feedthrough
PHY-3001 : eco cells: (33 66 1) is for feedthrough
PHY-3001 : eco cells: (34 1 1) is for feedthrough
PHY-3001 : eco cells: (34 2 0) is for feedthrough
PHY-3001 : eco cells: (34 4 1) is for feedthrough
PHY-3001 : eco cells: (34 5 0) is for feedthrough
PHY-3001 : eco cells: (34 5 3) is for feedthrough
PHY-3001 : eco cells: (34 6 2) is for feedthrough
PHY-3001 : eco cells: (34 6 3) is for feedthrough
PHY-3001 : eco cells: (34 9 1) is for feedthrough
PHY-3001 : eco cells: (34 10 1) is for feedthrough
PHY-3001 : eco cells: (34 11 0) is for feedthrough
PHY-3001 : eco cells: (34 12 0) is for feedthrough
PHY-3001 : eco cells: (34 12 1) is for feedthrough
PHY-3001 : eco cells: (34 13 0) is for feedthrough
PHY-3001 : eco cells: (34 15 0) is for feedthrough
PHY-3001 : eco cells: (34 15 3) is for feedthrough
PHY-3001 : eco cells: (34 16 0) is for feedthrough
PHY-3001 : eco cells: (34 16 1) is for feedthrough
PHY-3001 : eco cells: (34 16 2) is for feedthrough
PHY-3001 : eco cells: (34 19 0) is for feedthrough
PHY-3001 : eco cells: (34 20 3) is for feedthrough
PHY-3001 : eco cells: (34 32 3) is for feedthrough
PHY-3001 : eco cells: (34 34 2) is for feedthrough
PHY-3001 : eco cells: (34 36 2) is for feedthrough
PHY-3001 : eco cells: (34 38 0) is for feedthrough
PHY-3001 : eco cells: (34 40 1) is for feedthrough
PHY-3001 : eco cells: (34 41 0) is for feedthrough
PHY-3001 : eco cells: (34 43 3) is for feedthrough
PHY-3001 : eco cells: (34 56 1) is for feedthrough
PHY-3001 : eco cells: (34 58 0) is for feedthrough
PHY-3001 : eco cells: (34 61 0) is for feedthrough
PHY-3001 : eco cells: (34 62 0) is for feedthrough
PHY-3001 : eco cells: (34 63 0) is for feedthrough
PHY-3001 : eco cells: (34 64 3) is for feedthrough
PHY-3001 : eco cells: (34 65 2) is for feedthrough
PHY-3001 : eco cells: (35 2 0) is for feedthrough
PHY-3001 : eco cells: (35 4 0) is for feedthrough
PHY-3001 : eco cells: (35 7 3) is for feedthrough
PHY-3001 : eco cells: (35 8 0) is for feedthrough
PHY-3001 : eco cells: (35 8 1) is for feedthrough
PHY-3001 : eco cells: (35 8 3) is for feedthrough
PHY-3001 : eco cells: (35 9 3) is for feedthrough
PHY-3001 : eco cells: (35 10 1) is for feedthrough
PHY-3001 : eco cells: (35 10 2) is for feedthrough
PHY-3001 : eco cells: (35 11 0) is for feedthrough
PHY-3001 : eco cells: (35 13 0) is for feedthrough
PHY-3001 : eco cells: (35 13 2) is for feedthrough
PHY-3001 : eco cells: (35 14 0) is for feedthrough
PHY-3001 : eco cells: (35 14 1) is for feedthrough
PHY-3001 : eco cells: (35 15 0) is for feedthrough
PHY-3001 : eco cells: (35 15 1) is for feedthrough
PHY-3001 : eco cells: (35 15 2) is for feedthrough
PHY-3001 : eco cells: (35 16 0) is for feedthrough
PHY-3001 : eco cells: (35 16 1) is for feedthrough
PHY-3001 : eco cells: (35 17 2) is for feedthrough
PHY-3001 : eco cells: (35 19 1) is for feedthrough
PHY-3001 : eco cells: (35 19 2) is for feedthrough
PHY-3001 : eco cells: (35 20 0) is for feedthrough
PHY-3001 : eco cells: (35 20 1) is for feedthrough
PHY-3001 : eco cells: (35 23 0) is for feedthrough
PHY-3001 : eco cells: (35 28 0) is for feedthrough
PHY-3001 : eco cells: (35 34 3) is for feedthrough
PHY-3001 : eco cells: (35 37 1) is for feedthrough
PHY-3001 : eco cells: (35 38 0) is for feedthrough
PHY-3001 : eco cells: (35 39 0) is for feedthrough
PHY-3001 : eco cells: (35 40 0) is for feedthrough
PHY-3001 : eco cells: (35 41 1) is for feedthrough
PHY-3001 : eco cells: (35 42 2) is for feedthrough
PHY-3001 : eco cells: (35 44 3) is for feedthrough
PHY-3001 : eco cells: (35 60 1) is for feedthrough
PHY-3001 : eco cells: (35 63 1) is for feedthrough
PHY-3001 : eco cells: (35 63 3) is for feedthrough
PHY-3001 : eco cells: (35 65 3) is for feedthrough
PHY-3001 : eco cells: (35 67 2) is for feedthrough
PHY-3001 : eco cells: (35 70 0) is for feedthrough
PHY-3001 : eco cells: (36 2 0) is for feedthrough
PHY-3001 : eco cells: (36 5 0) is for feedthrough
PHY-3001 : eco cells: (36 5 1) is for feedthrough
PHY-3001 : eco cells: (36 6 0) is for feedthrough
PHY-3001 : eco cells: (36 6 1) is for feedthrough
PHY-3001 : eco cells: (36 8 0) is for feedthrough
PHY-3001 : eco cells: (36 8 1) is for feedthrough
PHY-3001 : eco cells: (36 9 0) is for feedthrough
PHY-3001 : eco cells: (36 9 1) is for feedthrough
PHY-3001 : eco cells: (36 9 3) is for feedthrough
PHY-3001 : eco cells: (36 10 1) is for feedthrough
PHY-3001 : eco cells: (36 10 2) is for feedthrough
PHY-3001 : eco cells: (36 11 0) is for feedthrough
PHY-3001 : eco cells: (36 14 1) is for feedthrough
PHY-3001 : eco cells: (36 15 1) is for feedthrough
PHY-3001 : eco cells: (36 15 3) is for feedthrough
PHY-3001 : eco cells: (36 16 0) is for feedthrough
PHY-3001 : eco cells: (36 18 1) is for feedthrough
PHY-3001 : eco cells: (36 18 3) is for feedthrough
PHY-3001 : eco cells: (36 19 0) is for feedthrough
PHY-3001 : eco cells: (36 19 1) is for feedthrough
PHY-3001 : eco cells: (36 20 3) is for feedthrough
PHY-3001 : eco cells: (36 22 2) is for feedthrough
PHY-3001 : eco cells: (36 22 3) is for feedthrough
PHY-3001 : eco cells: (36 35 2) is for feedthrough
PHY-3001 : eco cells: (36 38 0) is for feedthrough
PHY-3001 : eco cells: (36 38 1) is for feedthrough
PHY-3001 : eco cells: (36 40 0) is for feedthrough
PHY-3001 : eco cells: (36 60 3) is for feedthrough
PHY-3001 : eco cells: (36 63 1) is for feedthrough
PHY-3001 : eco cells: (37 2 0) is for feedthrough
PHY-3001 : eco cells: (37 2 1) is for feedthrough
PHY-3001 : eco cells: (37 5 1) is for feedthrough
PHY-3001 : eco cells: (37 8 1) is for feedthrough
PHY-3001 : eco cells: (37 9 0) is for feedthrough
PHY-3001 : eco cells: (37 10 3) is for feedthrough
PHY-3001 : eco cells: (37 11 0) is for feedthrough
PHY-3001 : eco cells: (37 12 1) is for feedthrough
PHY-3001 : eco cells: (37 13 0) is for feedthrough
PHY-3001 : eco cells: (37 13 1) is for feedthrough
PHY-3001 : eco cells: (37 15 0) is for feedthrough
PHY-3001 : eco cells: (37 15 1) is for feedthrough
PHY-3001 : eco cells: (37 15 3) is for feedthrough
PHY-3001 : eco cells: (37 16 0) is for feedthrough
PHY-3001 : eco cells: (37 17 1) is for feedthrough
PHY-3001 : eco cells: (37 17 2) is for feedthrough
PHY-3001 : eco cells: (37 26 0) is for feedthrough
PHY-3001 : eco cells: (37 38 2) is for feedthrough
PHY-3001 : eco cells: (37 40 0) is for feedthrough
PHY-3001 : eco cells: (37 47 3) is for feedthrough
PHY-3001 : eco cells: (37 51 1) is for feedthrough
PHY-3001 : eco cells: (37 54 3) is for feedthrough
PHY-3001 : eco cells: (37 56 1) is for feedthrough
PHY-3001 : eco cells: (37 62 1) is for feedthrough
PHY-3001 : eco cells: (37 70 1) is for feedthrough
PHY-3001 : eco cells: (38 2 0) is for feedthrough
PHY-3001 : eco cells: (38 3 3) is for feedthrough
PHY-3001 : eco cells: (38 4 0) is for feedthrough
PHY-3001 : eco cells: (38 5 1) is for feedthrough
PHY-3001 : eco cells: (38 6 2) is for feedthrough
PHY-3001 : eco cells: (38 7 2) is for feedthrough
PHY-3001 : eco cells: (38 9 1) is for feedthrough
PHY-3001 : eco cells: (38 10 2) is for feedthrough
PHY-3001 : eco cells: (38 12 0) is for feedthrough
PHY-3001 : eco cells: (38 12 1) is for feedthrough
PHY-3001 : eco cells: (38 12 2) is for feedthrough
PHY-3001 : eco cells: (38 13 1) is for feedthrough
PHY-3001 : eco cells: (38 14 0) is for feedthrough
PHY-3001 : eco cells: (38 14 1) is for feedthrough
PHY-3001 : eco cells: (38 15 3) is for feedthrough
PHY-3001 : eco cells: (38 16 0) is for feedthrough
PHY-3001 : eco cells: (38 16 1) is for feedthrough
PHY-3001 : eco cells: (38 17 1) is for feedthrough
PHY-3001 : eco cells: (38 35 2) is for feedthrough
PHY-3001 : eco cells: (38 36 2) is for feedthrough
PHY-3001 : eco cells: (38 50 1) is for feedthrough
PHY-3001 : eco cells: (38 55 2) is for feedthrough
PHY-3001 : eco cells: (38 64 1) is for feedthrough
PHY-3001 : eco cells: (38 66 0) is for feedthrough
PHY-3001 : eco cells: (38 67 1) is for feedthrough
PHY-3001 : eco cells: (39 6 1) is for feedthrough
PHY-3001 : eco cells: (39 9 3) is for feedthrough
PHY-3001 : eco cells: (39 11 0) is for feedthrough
PHY-3001 : eco cells: (39 11 1) is for feedthrough
PHY-3001 : eco cells: (39 12 0) is for feedthrough
PHY-3001 : eco cells: (39 13 2) is for feedthrough
PHY-3001 : eco cells: (39 13 3) is for feedthrough
PHY-3001 : eco cells: (39 40 1) is for feedthrough
PHY-3001 : eco cells: (39 49 3) is for feedthrough
PHY-3001 : eco cells: (39 67 0) is for feedthrough
PHY-3001 : eco cells: (39 69 3) is for feedthrough
PHY-3001 : eco cells: 9091 has valid locations, 1 needs to be replaced
PHY-3001 : design contains 9155 instances, 9050 slices, 291 macros(1458 instances: 956 mslices 502 lslices)
PHY-3001 : Start timing update ...
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.940618s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (99.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 710542
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 92%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(235): len = 710420, overlap = 0
PHY-3002 : Step(236): len = 710420, overlap = 0
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003950s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20693/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 894688, over cnt = 10(0%), over = 15, worst = 3
PHY-1002 : len = 894440, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 894584, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 894688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.430573s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (98.0%)

PHY-1001 : Congestion index: top1 = 55.69, top5 = 51.23, top10 = 48.33, top15 = 46.38.
PHY-3001 : End congestion estimation;  0.700053s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.278976s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.********
PHY-3002 : Step(237): len = 710420, overlap = 0
PHY-3002 : Step(238): len = 710420, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20700/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 894688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.163568s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.5%)

PHY-1001 : Congestion index: top1 = 55.69, top5 = 51.23, top10 = 48.33, top15 = 46.38.
PHY-3001 : End congestion estimation;  0.582465s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (96.6%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.670704s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (96.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0451113
PHY-3002 : Step(239): len = 710420, overlap = 0.25
PHY-3002 : Step(240): len = 710420, overlap = 0.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008037s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Trial Legalized: Len = 710478
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 94%
PHY-3001 : Analyzing congestion ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20693/22978.
PHY-1001 : Global iterations in 12 thread ...
PHY-1002 : len = 894824, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 894792, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 894872, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 894920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.532825s wall, 0.546875s user + 0.015625s system = 0.562500s CPU (105.6%)

PHY-1001 : Congestion index: top1 = 55.69, top5 = 51.20, top10 = 48.27, top15 = 46.34.
PHY-3001 : End congestion estimation;  0.930001s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (104.2%)

PHY-3001 : Update density targets...
PHY-3001 : Update congestion history...
PHY-3001 : Update timing in global mode ...
TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.102747s wall, 1.093750s user + 0.000000s system = 1.093750s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0147054
PHY-3002 : Step(241): len = 710223, overlap = 0.25
PHY-3002 : Step(242): len = 710210, overlap = 0
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005486s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 710562, Over = 0
PHY-3001 : End spreading;  0.073934s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.7%)

PHY-3001 : Final: Len = 710562, Over = 0
RUN-1003 : finish command "place -eco" in  8.202504s wall, 8.203125s user + 0.234375s system = 8.437500s CPU (102.9%)

RUN-1004 : used memory is 1228 MB, reserved memory is 1225 MB, peak memory is 1235 MB
RUN-1001 : Eco place succeeded
RUN-1002 : start command "route -eco"
RUN-1001 : Open license file D:/softwawe/Anlogic/TD_5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 12 thread(s)
RUN-1001 : There are total 9157 instances
RUN-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
RUN-1001 : There are total 22978 nets
RUN-1001 : 16856 nets have 2 pins
RUN-1001 : 4508 nets have [3 - 5] pins
RUN-1001 : 1022 nets have [6 - 10] pins
RUN-1001 : 361 nets have [11 - 20] pins
RUN-1001 : 217 nets have [21 - 99] pins
RUN-1001 : 14 nets have 100+ pins
PHY-1001 : 4525 mslices, 4525 lslices, 60 pads, 42 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 22976 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 1233, reserve = 1231, peak = 1235.
PHY-1001 : Detailed router is running in eco mode.
PHY-1001 : Refresh detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1025 : Net COM3/GNRMC/fifo_di[5]_dup_19 is open after eco import.
PHY-1025 : Net COM3/GNRMC/fifo_di[3]_dup_23 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/wr_en_s_dup_1 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/rd_addr[1] is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[248] is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[237] is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_4717 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_4725 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_4727 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_4729 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_13904 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_13906 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_13918 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/dob_tmp_b2[1]_syn_36 is open after eco import.
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/dob_tmp_b2[1]_syn_38 is open after eco import.
PHY-1001 : net COM3/GNRMC/u_fifo/ram_inst/clkb will be routed on clock mesh
PHY-1025 : Net COM3/GNRMC/u_fifo/ram_inst/clkb is open after eco import.
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : eco open net = 16
PHY-1001 : Current memory(MB): used = 1250, reserve = 1247, peak = 1250.
PHY-1001 : End build detailed router design. 2.110395s wall, 2.109375s user + 0.000000s system = 2.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End initial clock net routed; 0.995060s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (98.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033565s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.030007s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.031799s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.3%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.030232s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.4%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.06172e+06, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.032914s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.9%)

PHY-1001 : Current memory(MB): used = 1251, reserve = 1248, peak = 1251.
PHY-1001 : End phase 1; 1.191106s wall, 1.187500s user + 0.000000s system = 1.187500s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 94% nets.
PHY-1001 : Routed 94% nets.
PHY-1001 : Routed 94% nets.
PHY-1001 : Routed 94% nets.
PHY-1001 : Routed 94% nets.
PHY-1022 : len = 2.10779e+06, over cnt = 282(0%), over = 282, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 1251, reserve = 1248, peak = 1251.
PHY-1001 : End initial routed; 0.535920s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (99.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21749(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.209   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 4.056996s wall, 4.062500s user + 0.000000s system = 4.062500s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 1261, reserve = 1259, peak = 1261.
PHY-1001 : End phase 2; 4.593032s wall, 4.593750s user + 0.000000s system = 4.593750s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 2.10779e+06, over cnt = 282(0%), over = 282, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.269029s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 2.09825e+06, over cnt = 140(0%), over = 140, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.527150s wall, 0.546875s user + 0.031250s system = 0.578125s CPU (109.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 2.09774e+06, over cnt = 106(0%), over = 106, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.373674s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 2.09806e+06, over cnt = 88(0%), over = 88, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.374913s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 2.09831e+06, over cnt = 66(0%), over = 66, worst = 1, crit = 0
PHY-1001 : End DR Iter 4; 0.351454s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.8%)

PHY-1001 : ===== DR Iter 5 =====
PHY-1022 : len = 2.09874e+06, over cnt = 56(0%), over = 56, worst = 1, crit = 0
PHY-1001 : End DR Iter 5; 0.490489s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (98.8%)

PHY-1001 : ===== DR Iter 6 =====
PHY-1022 : len = 2.09916e+06, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End DR Iter 6; 0.405424s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.2%)

PHY-1001 : ===== DR Iter 7 =====
PHY-1022 : len = 2.09947e+06, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End DR Iter 7; 0.358165s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.3%)

PHY-1001 : ===== DR Iter 8 =====
PHY-1022 : len = 2.09978e+06, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 8; 0.282823s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (116.0%)

PHY-1001 : ==== DR Iter 9 ====
PHY-1022 : len = 2.09999e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 9; 0.202792s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (131.0%)

PHY-1001 : ==== DR Iter 10 ====
PHY-1022 : len = 2.09999e+06, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 10; 0.172708s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (108.6%)

PHY-1001 : ==== DR Iter 11 ====
PHY-1022 : len = 2.1e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 11; 0.193887s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.8%)

PHY-1001 : ==== DR Iter 12 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 12; 0.358073s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (104.7%)

PHY-1001 : ===== DR Iter 13 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 13; 0.157809s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.9%)

PHY-1001 : ==== DR Iter 14 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 14; 0.205250s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (106.6%)

PHY-1001 : ==== DR Iter 15 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 15; 0.218479s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (107.3%)

PHY-1001 : ==== DR Iter 16 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 16; 0.212153s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (103.1%)

PHY-1001 : ==== DR Iter 17 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 17; 0.298763s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.4%)

PHY-1001 : ==== DR Iter 18 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 18; 0.328854s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.8%)

PHY-1001 : ===== DR Iter 19 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 19; 0.186603s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.5%)

PHY-1001 : ==== DR Iter 20 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 20; 0.201717s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (100.7%)

PHY-1001 : ==== DR Iter 21 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 21; 0.233447s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (93.7%)

PHY-1001 : ==== DR Iter 22 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 22; 0.218335s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.2%)

PHY-1001 : ==== DR Iter 23 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 23; 0.304360s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (107.8%)

PHY-1001 : ==== DR Iter 24 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 24; 0.302097s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.3%)

PHY-1001 : ==== DR Iter 25 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 25; 0.604656s wall, 0.625000s user + 0.015625s system = 0.640625s CPU (105.9%)

PHY-1001 : ===== DR Iter 26 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 26; 0.190651s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.3%)

PHY-1001 : ==== DR Iter 27 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 27; 0.199899s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (132.9%)

PHY-1001 : ==== DR Iter 28 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 28; 0.216608s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (93.8%)

PHY-1001 : ==== DR Iter 29 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 29; 0.199680s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.7%)

PHY-1001 : ==== DR Iter 30 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 30; 0.230937s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.5%)

PHY-1001 : ==== DR Iter 31 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 31; 0.265957s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.9%)

PHY-1001 : ==== DR Iter 32 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 32; 0.467360s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (100.3%)

PHY-1001 : ==== DR Iter 33 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 33; 0.479389s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.8%)

PHY-1001 : ===== DR Iter 34 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 34; 0.159457s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (107.8%)

PHY-1001 : ==== DR Iter 35 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 35; 0.169345s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.5%)

PHY-1001 : ==== DR Iter 36 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 36; 0.186329s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.6%)

PHY-1001 : ==== DR Iter 37 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 37; 0.175637s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : ==== DR Iter 38 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 38; 0.238168s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (98.4%)

PHY-1001 : ==== DR Iter 39 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 39; 0.246905s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.3%)

PHY-1001 : ==== DR Iter 40 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 40; 0.461391s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (94.8%)

PHY-1001 : ==== DR Iter 41 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 41; 0.537260s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (101.8%)

PHY-1001 : ==== DR Iter 42 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 42; 0.532521s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (99.8%)

PHY-1001 : ===== DR Iter 43 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 43; 0.156623s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (119.7%)

PHY-1001 : ==== DR Iter 44 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 44; 0.168727s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (101.9%)

PHY-1001 : ==== DR Iter 45 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 45; 0.189752s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (115.3%)

PHY-1001 : ==== DR Iter 46 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 46; 0.192830s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (121.5%)

PHY-1001 : ==== DR Iter 47 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 47; 0.251985s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.2%)

PHY-1001 : ==== DR Iter 48 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 48; 0.265614s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.0%)

PHY-1001 : ==== DR Iter 49 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 49; 0.516279s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.9%)

PHY-1001 : ==== DR Iter 50 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 50; 0.485038s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (99.9%)

PHY-1001 : ==== DR Iter 51 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 51; 0.520681s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.0%)

PHY-1001 : ==== DR Iter 52 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 52; 0.523335s wall, 0.531250s user + 0.015625s system = 0.546875s CPU (104.5%)

PHY-1001 : ===== DR Iter 53 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 53; 0.152196s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.7%)

PHY-1001 : ==== DR Iter 54 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 54; 0.180457s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (121.2%)

PHY-1001 : ==== DR Iter 55 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 55; 0.184351s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.7%)

PHY-1001 : ==== DR Iter 56 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 56; 0.185304s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.2%)

PHY-1001 : ==== DR Iter 57 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 57; 0.242735s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (109.4%)

PHY-1001 : ==== DR Iter 58 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 58; 0.254771s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.1%)

PHY-1001 : ==== DR Iter 59 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 59; 0.452203s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.2%)

PHY-1001 : ==== DR Iter 60 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 60; 0.439854s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.5%)

PHY-1001 : ==== DR Iter 61 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 61; 0.435087s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.6%)

PHY-1001 : ==== DR Iter 62 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 62; 0.453128s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.0%)

PHY-1001 : ==== DR Iter 63 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 63; 0.469300s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (99.9%)

PHY-1001 : ===== DR Iter 64 =====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 64; 0.155668s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (100.4%)

PHY-1001 : ==== DR Iter 65 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 65; 0.162817s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (115.2%)

PHY-1001 : ==== DR Iter 66 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 66; 0.180709s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.8%)

PHY-1001 : ==== DR Iter 67 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 67; 0.208650s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.4%)

PHY-1001 : ==== DR Iter 68 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 68; 0.255980s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.8%)

PHY-1001 : ==== DR Iter 69 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 69; 0.240567s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (97.4%)

PHY-1001 : ==== DR Iter 70 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 70; 0.420975s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.2%)

PHY-1001 : ==== DR Iter 71 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 71; 0.465517s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (100.7%)

PHY-1001 : ==== DR Iter 72 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 72; 0.523435s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (98.5%)

PHY-1001 : ==== DR Iter 73 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 73; 0.575348s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (100.5%)

PHY-1001 : ==== DR Iter 74 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 74; 0.592903s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (100.1%)

PHY-1001 : ==== DR Iter 75 ====
PHY-1022 : len = 2.0999e+06, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 75; 0.595477s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/21749(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.209   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 3.912453s wall, 3.906250s user + 0.000000s system = 3.906250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 1267, reserve = 1266, peak = 1267.
PHY-1001 : End phase 3; 27.956162s wall, 28.156250s user + 0.343750s system = 28.500000s CPU (101.9%)

PHY-5014 WARNING: Detail route doesn't find pib for CLK100M/pll_inst.fbclk[0]
PHY-1001 : 1098 feed throughs used by 786 nets
PHY-1001 : Current memory(MB): used = 1278, reserve = 1277, peak = 1278.
PHY-1001 : End export database. 2.829105s wall, 2.812500s user + 0.015625s system = 2.828125s CPU (100.0%)

PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x27y10_local0), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_14112 COM3/GNRMC/u_fifo/rd_addr[1]
PHY-8023 ERROR: Location: (x37y12_s2beg3), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[215] COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[211]
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route -eco" in  40.181853s wall, 40.343750s user + 0.375000s system = 40.718750s CPU (101.3%)

RUN-1004 : used memory is 1273 MB, reserved memory is 1272 MB, peak memory is 1278 MB
RUN-8102 ERROR: Incremental route failed
PHY-1001 : Routing violations:
PHY-8023 ERROR: Location: (x27y10_local0), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_14112 COM3/GNRMC/u_fifo/rd_addr[1]
PHY-8023 ERROR: Location: (x37y12_s2beg3), nets: COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[215] COM3/GNRMC/u_fifo/ram_inst/ramread0_syn_2315[211]
PHY-1001 : End of Routing Violations.
RUN-1003 : finish command "route" in  191.603625s wall, 229.500000s user + 1.453125s system = 230.953125s CPU (120.5%)

RUN-1004 : used memory is 1273 MB, reserved memory is 1272 MB, peak memory is 1278 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250710_171401.log"
